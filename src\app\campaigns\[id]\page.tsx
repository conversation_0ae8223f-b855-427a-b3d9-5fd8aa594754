'use client';

import { useEffect, useState } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import {
  getCampaignWithDetails,
  hasInfluencerApplied,
  withdrawCampaignApplication,
} from '@/lib/campaigns';
import { CampaignDetails, InfluencerApplicationResponse } from '@/lib/types';
import { getOrCreateProfile } from '@/lib/profiles';
import {
  canActivateCampaign,
  activateCampaign as activateCampaignFunction,
  deactivateCampaign,
} from '@/lib/subscriptions';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import { toast } from 'sonner';
import { WithdrawApplicationModal } from '@/components/applications/WithdrawApplicationModal';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { GradientButton } from '@/components/ui/gradient-button';
import { BackButton } from '@/components/ui/back-button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import PlatformIconSimple from '@/components/ui/platform-icon-simple';
import {
  Loader2,
  Users,
  Tag,
  Clock,
  AlertCircle,
  Send,
  Edit,
  Hash,
  Ban,
  FileText,
  Handshake,
  RotateCcw,
  Users2,
  Building2,
  Crown,
  MessageCircle,
  CheckCircle,
  X,
} from 'lucide-react';
import Link from 'next/link';
import { formatDistanceToNow } from 'date-fns';
import { bs } from 'date-fns/locale';
import { CampaignApplicationForm } from '@/components/campaigns/campaign-application-form';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { UpgradeRequiredModal } from '@/components/modals/UpgradeRequiredModal';
import { PromoteCampaignModal } from '@/components/modals/PromoteCampaignModal';
import {
  checkCampaignApplicationLimit,
  CampaignApplicationLimitResult,
} from '@/lib/campaign-application-limits';
import { supabase } from '@/lib/supabase';

export default function CampaignDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const { user, loading: authLoading } = useAuth();
  const [campaign, setCampaign] = useState<CampaignDetails | null>(null);
  const [profile, setProfile] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showApplicationForm, setShowApplicationForm] = useState(false);
  const [isOwner, setIsOwner] = useState(false);
  const [isActivating, setIsActivating] = useState(false);
  const [applicationStatus, setApplicationStatus] =
    useState<InfluencerApplicationResponse>({ hasApplied: false });
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);
  const [applicationLimitResult, setApplicationLimitResult] =
    useState<CampaignApplicationLimitResult | null>(null);
  const [upgradeModalData, setUpgradeModalData] = useState<{
    currentCount: number;
    limit: number;
  } | null>(null);
  const [showPromoteModal, setShowPromoteModal] = useState(false);
  const [userSubscriptionType, setUserSubscriptionType] = useState<
    'free' | 'premium'
  >('free');
  const [isWithdrawing, setIsWithdrawing] = useState(false);
  const [showWithdrawModal, setShowWithdrawModal] = useState(false);

  const campaignId = params.id as string;

  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/prijava');
      return;
    }

    if (user && campaignId) {
      loadData();
    }
  }, [user, authLoading, campaignId, router]);

  const loadData = async () => {
    try {
      setLoading(true);

      // Load profile first
      const { data: profileData, error: profileError } =
        await getOrCreateProfile(user!.id);
      if (profileError || !profileData) {
        setError('Greška pri učitavanju profila');
        return;
      }
      setProfile(profileData);

      // Load campaign details
      const { data, error } = await getCampaignWithDetails(campaignId);

      if (error) {
        setError('Greška pri učitavanju kampanje');
        return;
      }

      if (!data) {
        setError('Kampanja nije pronađena');
        return;
      }

      setCampaign(data);

      // Check if current user is the owner of the campaign
      setIsOwner(data?.business_id === user?.id);

      // Load user subscription type if user is owner
      if (data?.business_id === user?.id) {
        const { data: business } = await supabase
          .from('businesses')
          .select('subscription_type')
          .eq('id', user.id)
          .single();

        if (business) {
          setUserSubscriptionType(
            (business.subscription_type as 'free' | 'premium') || 'free'
          );
        }
      }

      // Check if influencer already applied (only for non-owners)
      if (data?.business_id !== user?.id && user?.id) {
        const { data: applicationData } = await hasInfluencerApplied(
          campaignId,
          user.id
        );
        if (applicationData) {
          setApplicationStatus(applicationData);
        }

        // Check campaign application limit for influencers
        const limitResult = await checkCampaignApplicationLimit(user.id);
        setApplicationLimitResult(limitResult);
      }
    } catch {
      setError('Neočekivana greška');
    } finally {
      setLoading(false);
    }
  };

  if (authLoading || loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (error || !campaign) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <AlertCircle className="h-12 w-12 text-destructive mx-auto mb-4" />
            <CardTitle>Greška</CardTitle>
            <CardDescription>
              {error || 'Kampanja nije pronađena'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button
              variant="outline"
              className="w-full"
              onClick={() => router.back()}
            >
              Nazad
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // const getStatusBadge = (status: string) => {
  //   const statusMap = {
  //     draft: { label: 'Nacrt', variant: 'secondary' as const },
  //     active: { label: 'Aktivna', variant: 'default' as const },
  //     paused: { label: 'Pauzirana', variant: 'outline' as const },
  //     completed: { label: 'Završena', variant: 'secondary' as const },
  //     cancelled: { label: 'Otkazana', variant: 'destructive' as const },
  //   };
  //
  //   const statusInfo =
  //     statusMap[status as keyof typeof statusMap] || statusMap.draft;
  //   return <Badge variant={statusInfo.variant}>{statusInfo.label}</Badge>;
  // };

  const getCollaborationTypeLabel = (type: string) => {
    const typeMap = {
      paid: 'Plaćena saradnja',
      barter: 'Barter (razmena)',
      hybrid: 'Hibridna saradnja',
    };
    return typeMap[type as keyof typeof typeMap] || type;
  };

  // const getPaymentTermsLabel = (terms: string) => {
  //   const termsMap = {
  //     upfront: 'Unapred',
  //     '50_50': '50% unapred, 50% po završetku',
  //     on_delivery: 'Po isporuci',
  //   };
  //   return termsMap[terms as keyof typeof termsMap] || terms;
  // };

  // const getUsageRightsLabel = (rights: string) => {
  //   const rightsMap = {
  //     single_use: 'Jednokratna upotreba',
  //     unlimited: 'Neograničena upotreba',
  //     '6_months': '6 meseci',
  //     '1_year': '1 godina',
  //   };
  //   return rightsMap[rights as keyof typeof rightsMap] || rights;
  // };

  const getGenderLabel = (gender: string) => {
    const genderMap = {
      male: 'Muški',
      female: 'Ženski',
      other: 'Ostalo',
      prefer_not_to_say: 'Ne želim da kažem',
    };
    return genderMap[gender as keyof typeof genderMap] || 'Svi';
  };

  const handleActivateCampaign = async () => {
    if (!campaign || !user?.id) return;

    setIsActivating(true);
    try {
      console.log('Activating campaign:', campaign.id, 'for user:', user.id);

      // Check subscription limits before activation
      const activationCheck = await canActivateCampaign(user.id);

      if (!activationCheck.canActivate) {
        if (activationCheck.reason === 'free_limit_reached') {
          setUpgradeModalData({
            currentCount: activationCheck.activeCount,
            limit: activationCheck.maxAllowed,
          });
          setShowUpgradeModal(true);
          return;
        }
      }

      // Use our new activation function
      const success = await activateCampaignFunction(campaign.id, user.id);

      if (!success) {
        console.error('Failed to activate campaign');
        toast.error('Greška pri aktiviranju kampanje');
        return;
      }

      console.log('Campaign activated successfully');
      // Update local state
      setCampaign(prev => (prev ? { ...prev, status: 'active' } : null));
      toast.success('Kampanja je uspješno aktivirana!');
    } catch (error) {
      console.error('Error activating campaign:', error);
      toast.error('Greška pri aktiviranju kampanje');
    } finally {
      setIsActivating(false);
    }
  };

  const handleDeactivateCampaign = async () => {
    if (!campaign || !user?.id) return;

    // Provjeri ima li aplikacija
    if (campaign.applications_count && campaign.applications_count > 0) {
      toast.error('Ne možete deaktivirati kampanju koja već ima aplikacije!');
      return;
    }

    setIsActivating(true);
    try {
      const success = await deactivateCampaign(campaign.id, user.id);

      if (!success) {
        toast.error('Greška pri deaktiviranju kampanje');
        return;
      }

      // Update local state
      setCampaign(prev => (prev ? { ...prev, status: 'draft' } : null));
      toast.success('Kampanja je uspješno deaktivirana!');
    } catch (error) {
      console.error('Error deactivating campaign:', error);
      toast.error('Greška pri deaktiviranju kampanje');
    } finally {
      setIsActivating(false);
    }
  };

  const handlePromoteCampaign = () => {
    if (!campaign || !user?.id) return;
    setShowPromoteModal(true);
  };

  const handlePromotionSuccess = () => {
    // Refresh campaign data to show featured status
    loadData();
  };

  const handleApplicationClick = () => {
    if (!applicationLimitResult) return;

    if (!applicationLimitResult.canApply) {
      setShowUpgradeModal(true);
      return;
    }

    setShowApplicationForm(true);
  };

  const handleWithdrawApplication = () => {
    if (!applicationStatus.application?.id) {
      toast.error('Greška: ID aplikacije nije pronađen');
      return;
    }

    setShowWithdrawModal(true);
  };

  const confirmWithdrawApplication = async () => {
    if (!applicationStatus.application?.id) return;

    setShowWithdrawModal(false);
    setIsWithdrawing(true);
    try {
      const { error } = await withdrawCampaignApplication(
        applicationStatus.application.id
      );

      if (error) {
        toast.error(error.message || 'Greška pri povlačenju aplikacije');
        return;
      }

      // Update the application status to show as not applied
      setApplicationStatus({ hasApplied: false });
      toast.success('Aplikacija je uspješno povučena');
    } catch (error) {
      console.error('Error withdrawing application:', error);
      toast.error('Neočekivana greška pri povlačenju aplikacije');
    } finally {
      setIsWithdrawing(false);
    }
  };

  // Use actual profile data for the header
  // const headerProfile = profile || {
  //   avatar_url: user?.user_metadata?.avatar_url,
  //   full_name: user?.user_metadata?.full_name,
  //   username: user?.user_metadata?.username || user?.email?.split('@')[0],
  // };

  return (
    <DashboardLayout requiredUserType={profile?.user_type || 'influencer'}>
      {/* Main Container with gradient background */}
      <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
        {/* Dreamy gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />

        {/* Subtle glow effect */}
        <div className="absolute inset-0 bg-gradient-to-r from-purple-300/20 via-pink-300/10 to-purple-400/20 dark:from-purple-600/10 dark:via-pink-600/5 dark:to-purple-500/10 blur-xl opacity-50" />

        <div className="relative p-6 space-y-6">
          {/* Header */}
          <div className="flex items-start gap-4">
            {/* Back button - hidden on mobile */}
            <div className="hidden md:block">
              <BackButton onClick={() => router.back()} />
            </div>

            <div className="flex-1 min-w-0">
              <h1 className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                {campaign.title}
              </h1>
              <div className="flex flex-wrap items-center gap-4 text-sm mt-2">
                <div className="flex items-center gap-2">
                  <MessageCircle className="h-4 w-4 text-purple-500" />
                  <span className="font-medium text-gray-900 dark:text-gray-100">
                    {campaign.applications_count || 0}
                  </span>
                  <span className="text-gray-600 dark:text-gray-400">
                    {(campaign.applications_count || 0) === 1
                      ? 'aplikacija'
                      : 'aplikacija'}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-pink-500" />
                  <span className="text-gray-600 dark:text-gray-400">
                    Objavljena{' '}
                    {campaign.created_at &&
                      formatDistanceToNow(new Date(campaign.created_at), {
                        addSuffix: true,
                        locale: bs,
                      })}
                  </span>
                </div>
              </div>
            </div>

            {/* Status badge */}
            {campaign.status && (
              <Badge
                variant="outline"
                className={`${
                  campaign.status === 'active'
                    ? 'bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 border-green-200'
                    : 'bg-gradient-to-r from-gray-100 to-slate-100 text-gray-800 border-gray-200'
                } font-medium`}
              >
                {campaign.status === 'active' ? 'Aktivna' : 'Neaktivna'}
              </Badge>
            )}
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-6">
              {/* Description */}
              <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
                <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
                <div className="relative p-6">
                  <div className="flex items-center gap-2 mb-4">
                    <FileText className="h-5 w-5 text-purple-500" />
                    <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                      Opis kampanje
                    </h3>
                  </div>
                  <p className="text-gray-700 dark:text-gray-300 leading-relaxed whitespace-pre-wrap">
                    {campaign.description}
                  </p>
                </div>
              </div>

              {/* Platforms and Content Types */}
              {campaign.platforms && campaign.platforms.length > 0 && (
                <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
                  <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
                  <div className="relative p-6">
                    <div className="flex items-center gap-2 mb-2">
                      <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                        Platforme i tipovi sadržaja
                      </h3>
                    </div>
                    <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">
                      Gde influencer treba da objavi sadržaj i koji tip sadržaja
                    </p>
                    <div className="space-y-4">
                      {campaign.platforms.map((platform, index) => (
                        <div
                          key={index}
                          className="relative overflow-hidden rounded-lg bg-gradient-to-br from-purple-50/60 via-pink-50/40 to-purple-100/60 dark:from-purple-900/20 dark:via-pink-900/10 dark:to-purple-800/25 border border-purple-200/40 dark:border-purple-700/30 p-4"
                        >
                          <div className="flex items-center gap-2 mb-3">
                            <PlatformIconSimple
                              platform={platform.name}
                              size="lg"
                            />
                            <span className="font-medium text-gray-800 dark:text-gray-200">
                              {platform.name}
                            </span>
                          </div>
                          <div className="flex flex-wrap gap-2">
                            {platform.content_types?.map((type, typeIndex) => (
                              <Badge
                                key={typeIndex}
                                variant="secondary"
                                className="bg-gradient-to-r from-violet-100 to-purple-100 text-violet-700 border-violet-200"
                              >
                                {type === 'post'
                                  ? 'Photo post'
                                  : type === 'video'
                                    ? 'Video'
                                    : type === 'story'
                                      ? 'Story'
                                      : type === 'reel'
                                        ? 'Shorts'
                                        : type}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              {/* Hashtags */}
              {campaign.hashtags && campaign.hashtags.length > 0 && (
                <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
                  <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
                  <div className="relative p-6">
                    <div className="flex items-center gap-2 mb-4">
                      <Hash className="h-5 w-5 text-purple-500" />
                      <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                        Obavezni hashtag-ovi
                      </h3>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {campaign.hashtags.map((hashtag, index) => (
                        <Badge
                          key={index}
                          variant="outline"
                          className="font-mono bg-gradient-to-r from-violet-100 to-purple-100 text-violet-700 border-violet-200"
                        >
                          #{hashtag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              {/* Do Not Mention */}
              {campaign.do_not_mention &&
                campaign.do_not_mention.length > 0 && (
                  <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
                    <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
                    <div className="relative p-6">
                      <div className="flex items-center gap-2 mb-4">
                        <Ban className="h-5 w-5 text-red-500" />
                        <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                          Ne spominjati
                        </h3>
                      </div>
                      <div className="flex flex-wrap gap-2">
                        {campaign.do_not_mention.map((item, index) => (
                          <Badge
                            key={index}
                            className="bg-gradient-to-r from-red-100 to-pink-100 text-red-800 border-red-200"
                          >
                            {item}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                )}

              {/* Target Audience */}
              {(campaign.age_range_min ||
                campaign.age_range_max ||
                campaign.gender) && (
                <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
                  <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
                  <div className="relative p-6">
                    <div className="flex items-center gap-2 mb-4">
                      <Users2 className="h-5 w-5 text-purple-500" />
                      <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                        Traženi profil influencera
                      </h3>
                    </div>
                    <div className="space-y-4">
                      {/* Age Range */}
                      {(campaign.age_range_min || campaign.age_range_max) && (
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600 dark:text-gray-400">
                            Uzrast
                          </span>
                          <span className="font-medium text-gray-900 dark:text-gray-100">
                            {campaign.age_range_min || 13} -{' '}
                            {campaign.age_range_max || 100} godina
                          </span>
                        </div>
                      )}

                      {/* Gender */}
                      {campaign.gender && campaign.gender !== 'all' && (
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600 dark:text-gray-400">
                            Pol
                          </span>
                          <span className="font-medium text-gray-900 dark:text-gray-100">
                            {getGenderLabel(campaign.gender)}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}

              {/* Content Categories */}
              {campaign.categories && campaign.categories.length > 0 && (
                <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
                  <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
                  <div className="relative p-6">
                    <div className="flex items-center gap-2 mb-4">
                      <Tag className="h-5 w-5 text-purple-500" />
                      <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                        Kategorije sadržaja koji influencer inače kreira
                      </h3>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {campaign.categories.map((category: any) => (
                        <Badge
                          key={category.id}
                          variant="secondary"
                          className="flex items-center gap-1 bg-gradient-to-r from-violet-100 to-purple-100 text-violet-700 border-violet-200"
                        >
                          {category.icon && (
                            <span className="text-sm">{category.icon}</span>
                          )}
                          {category.name}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              {/* Collaboration Type */}
              <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
                <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
                <div className="relative p-6">
                  <div className="flex items-center gap-2 mb-4">
                    <Handshake className="h-5 w-5 text-purple-500" />
                    <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                      Tip saradnje
                    </h3>
                  </div>
                  <p className="font-medium text-gray-900 dark:text-gray-100">
                    {getCollaborationTypeLabel(
                      campaign.collaboration_type || 'paid'
                    )}
                  </p>
                </div>
              </div>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Additional Notes */}
              {campaign.additional_notes && (
                <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
                  <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
                  <div className="relative p-6">
                    <div className="flex items-center gap-2 mb-4">
                      <FileText className="h-5 w-5 text-purple-500" />
                      <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                        Dodatne napomene
                      </h3>
                    </div>
                    <p className="text-gray-700 dark:text-gray-300 leading-relaxed whitespace-pre-wrap">
                      {campaign.additional_notes}
                    </p>
                  </div>
                </div>
              )}

              {/* Business Info */}
              <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
                <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
                <div className="relative p-6">
                  <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-4">
                    Biznis
                  </h3>

                  <div className="flex items-center gap-3">
                    <Avatar className="h-16 w-16 border-2 border-white/50">
                      <AvatarImage
                        src={campaign.business_avatar || undefined}
                        alt={campaign.company_name}
                      />
                      <AvatarFallback className="bg-gradient-to-r from-purple-100 to-pink-100 text-purple-700 text-lg">
                        <Building2 className="h-6 w-6" />
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <h4 className="font-semibold text-lg text-gray-900 dark:text-gray-100">
                        {campaign.company_name}
                      </h4>
                      <p className="text-gray-600 dark:text-gray-400">
                        @{campaign.business_username}
                      </p>
                    </div>
                  </div>

                  {campaign.business_username && !isOwner && (
                    <Link href={`/business/${campaign.business_username}`}>
                      <button className="w-full mt-4 flex items-center justify-center gap-2 px-3 py-2 text-sm font-medium bg-white/70 dark:bg-gray-800/50 hover:bg-white dark:hover:bg-gray-800/70 border border-gray-200/50 dark:border-gray-700/50 rounded-lg transition-all duration-200 hover:shadow-md">
                        <Building2 className="h-4 w-4" />
                        Pogledaj biznis profil
                      </button>
                    </Link>
                  )}
                </div>
              </div>

              {/* Campaign Details */}
              <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
                <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
                <div className="relative p-6">
                  <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-4">
                    Detalji kampanje
                  </h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        Budžet
                      </span>
                      <span className="font-semibold text-purple-700 dark:text-purple-300">
                        {campaign.budget?.toLocaleString() ||
                          'Nije specificirano'}{' '}
                        €
                      </span>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        Tip saradnje
                      </span>
                      <span className="font-medium text-gray-900 dark:text-gray-100">
                        {getCollaborationTypeLabel(
                          campaign.collaboration_type || 'paid'
                        )}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Action Buttons - Different for Owner vs Influencer */}
              <div className="space-y-3">
                {isOwner ? (
                  // Owner (Business) View
                  <>
                    {campaign.status === 'draft' ? (
                      // Draft campaign - show Activate button
                      <GradientButton
                        gradientVariant="primary"
                        className="w-full"
                        size="lg"
                        onClick={handleActivateCampaign}
                        disabled={isActivating}
                      >
                        {isActivating ? (
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        ) : (
                          <CheckCircle className="mr-2 h-5 w-5" />
                        )}
                        {isActivating ? 'Aktiviranje...' : 'Aktiviraj kampanju'}
                      </GradientButton>
                    ) : (
                      // Active campaign - show Applications button and Deactivate button
                      <>
                        <GradientButton
                          gradientVariant="primary"
                          className="w-full"
                          size="lg"
                          onClick={() =>
                            router.push(
                              `/dashboard/biznis/applications?campaign=${campaign.id}`
                            )
                          }
                        >
                          <Users className="mr-2 h-5 w-5" />
                          Pregled aplikacija ({campaign.applications_count})
                        </GradientButton>

                        {/* Promote button for premium users and active campaigns that are not featured */}
                        {userSubscriptionType === 'premium' &&
                          !campaign.is_featured && (
                            <Button
                              className="w-full bg-gradient-to-r from-yellow-100 to-yellow-200 hover:from-yellow-200 hover:to-yellow-300 border border-yellow-300 text-yellow-800"
                              size="lg"
                              onClick={handlePromoteCampaign}
                            >
                              <Crown className="mr-2 h-5 w-5" />
                              Promoviši kampanju
                            </Button>
                          )}

                        <Button
                          variant="outline"
                          className="w-full"
                          size="lg"
                          onClick={handleDeactivateCampaign}
                          disabled={
                            isActivating ||
                            (campaign.applications_count &&
                              campaign.applications_count > 0)
                          }
                        >
                          {isActivating ? (
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          ) : (
                            <RotateCcw className="mr-2 h-5 w-5" />
                          )}
                          {isActivating
                            ? 'Deaktiviram...'
                            : campaign.applications_count &&
                                campaign.applications_count > 0
                              ? 'Ne može se deaktivirati (ima aplikacije)'
                              : 'Deaktiviraj kampanju'}
                        </Button>
                      </>
                    )}

                    {/* Edit button only for draft campaigns */}
                    {campaign.status === 'draft' && (
                      <Button
                        variant="outline"
                        className="w-full"
                        size="lg"
                        onClick={() =>
                          router.push(`/campaigns/${campaign.id}/edit`)
                        }
                      >
                        <Edit className="mr-2 h-5 w-5" />
                        Uredi kampanju
                      </Button>
                    )}
                  </>
                ) : (
                  // Influencer View
                  <>
                    {applicationStatus.hasApplied ? (
                      // Show application status
                      <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg w-full">
                        <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
                        <div className="relative p-6">
                          <div className="flex items-center gap-3 mb-4">
                            <CheckCircle className="h-6 w-6 text-green-500" />
                            <div>
                              <h3 className="font-semibold text-gray-900 dark:text-gray-100">
                                Aplikacija poslana
                              </h3>
                              <p className="text-sm text-gray-600 dark:text-gray-400">
                                Prijavili ste se na ovu kampanju{' '}
                                {applicationStatus.appliedAt &&
                                  formatDistanceToNow(
                                    new Date(applicationStatus.appliedAt),
                                    { addSuffix: true, locale: bs }
                                  )}
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge
                              variant="outline"
                              className={
                                applicationStatus.application?.status ===
                                'pending'
                                  ? 'bg-gradient-to-r from-yellow-100 to-amber-100 text-yellow-800 border-yellow-200 dark:from-yellow-900/30 dark:to-amber-900/30 dark:text-yellow-300 dark:border-yellow-700'
                                  : applicationStatus.application?.status ===
                                      'accepted'
                                    ? 'bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 border-green-200 dark:from-green-900/30 dark:to-emerald-900/30 dark:text-green-300 dark:border-green-700'
                                    : applicationStatus.application?.status ===
                                        'rejected'
                                      ? 'bg-gradient-to-r from-red-100 to-rose-100 text-red-800 border-red-200 dark:from-red-900/30 dark:to-rose-900/30 dark:text-red-300 dark:border-red-700'
                                      : ''
                              }
                            >
                              {applicationStatus.application?.status ===
                                'pending' && 'Čeka se odgovor'}
                              {applicationStatus.application?.status ===
                                'accepted' && 'Prihvaćeno'}
                              {applicationStatus.application?.status ===
                                'rejected' && 'Odbačeno'}
                            </Badge>
                          </div>
                          {applicationStatus.application?.status ===
                            'pending' && (
                            <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
                              Biznis će uskoro pregledati vašu aplikaciju i
                              odgovoriti.
                            </p>
                          )}
                          {applicationStatus.application?.id && (
                            <div className="mt-4 space-y-2">
                              <Button
                                size="sm"
                                className="w-full bg-instagram-subtle text-white hover:opacity-90 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.02] hover:shadow-blue-200/50 dark:hover:shadow-blue-900/30"
                                onClick={() =>
                                  router.push(
                                    `/dashboard/influencer/applications/${applicationStatus.application?.id}`
                                  )
                                }
                              >
                                Pogledajte detalje vaše aplikacije
                              </Button>

                              {/* Withdraw button - only show if application is pending */}
                              {applicationStatus.application?.status ===
                                'pending' && (
                                <Button
                                  size="sm"
                                  variant="outline"
                                  className="w-full border-red-200 text-red-600 hover:bg-red-50 hover:border-red-300 hover:text-red-700 dark:border-red-800 dark:text-red-400 dark:hover:bg-red-950/20"
                                  onClick={handleWithdrawApplication}
                                  disabled={isWithdrawing}
                                >
                                  {isWithdrawing ? (
                                    <>
                                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                      Povlačim...
                                    </>
                                  ) : (
                                    <>
                                      <X className="mr-2 h-4 w-4" />
                                      Povuci aplikaciju
                                    </>
                                  )}
                                </Button>
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                    ) : (
                      // Show application form
                      <>
                        <GradientButton
                          gradientVariant="primary"
                          className="w-full"
                          size="lg"
                          onClick={handleApplicationClick}
                        >
                          <Send className="mr-2 h-5 w-5" />
                          Apliciraj
                        </GradientButton>

                        <Dialog
                          open={showApplicationForm}
                          onOpenChange={setShowApplicationForm}
                        >
                          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                            <DialogHeader>
                              <DialogTitle>Aplikacija za kampanju</DialogTitle>
                            </DialogHeader>
                            <CampaignApplicationForm
                              campaign={{
                                id: campaign.id,
                                title: campaign.title,
                                budget: campaign.budget,
                                description: campaign.description,
                                company_name: campaign.company_name,
                                platforms: campaign.platforms,
                              }}
                              onSuccess={() => {
                                setShowApplicationForm(false);
                                // Reload to show application status
                                loadData();
                              }}
                              onCancel={() => setShowApplicationForm(false)}
                            />
                          </DialogContent>
                        </Dialog>
                      </>
                    )}
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      <UpgradeRequiredModal
        isOpen={showUpgradeModal}
        onClose={() => setShowUpgradeModal(false)}
        feature={
          applicationLimitResult?.isFreePlan &&
          !applicationLimitResult?.canApply
            ? 'campaign_application_limit'
            : 'campaign_activation'
        }
        currentCount={
          applicationLimitResult?.isFreePlan &&
          !applicationLimitResult?.canApply
            ? applicationLimitResult.totalApplicationsThisMonth
            : upgradeModalData?.currentCount
        }
        limit={
          applicationLimitResult?.isFreePlan &&
          !applicationLimitResult?.canApply
            ? 5
            : upgradeModalData?.limit
        }
        resetDate={
          applicationLimitResult?.isFreePlan &&
          !applicationLimitResult?.canApply
            ? applicationLimitResult.resetDate
            : undefined
        }
      />

      <PromoteCampaignModal
        isOpen={showPromoteModal}
        onClose={() => setShowPromoteModal(false)}
        campaign={
          campaign
            ? {
                id: campaign.id,
                title: campaign.title,
                business_id: user?.id || '',
              }
            : null
        }
        onSuccess={handlePromotionSuccess}
      />

      <WithdrawApplicationModal
        isOpen={showWithdrawModal}
        onClose={() => setShowWithdrawModal(false)}
        onConfirm={confirmWithdrawApplication}
        isLoading={isWithdrawing}
      />
    </DashboardLayout>
  );
}
