import { NextRequest, NextResponse } from 'next/server';
import { headers } from 'next/headers';
import { stripe } from '@/lib/stripe';
import { createServerClient } from '@/lib/supabase';
import { FEATURED_CAMPAIGN_PRICES } from '@/lib/featured-campaigns';
import type Stripe from 'stripe';
import type { SupabaseClient } from '@supabase/supabase-js';

const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET!;

export async function POST(req: NextRequest) {
  try {
    const supabase = createServerClient();

    if (!stripe) {
      return NextResponse.json(
        { error: 'Stripe not configured' },
        { status: 500 }
      );
    }

    const body = await req.text();
    const headersList = await headers();
    const signature = headersList.get('stripe-signature')!;

    let event;

    try {
      event = stripe.webhooks.constructEvent(body, signature, webhookSecret);
    } catch (err) {
      console.error('Webhook signature verification failed:', err);
      return NextResponse.json({ error: 'Invalid signature' }, { status: 400 });
    }

    // Handle the checkout.session.completed event
    if (event.type === 'checkout.session.completed') {
      const session = event.data.object;

      // Extract metadata
      const { type } = session.metadata || {};

      // Handle different payment types
      if (type === 'featured_campaign') {
        return await handleFeaturedCampaignPayment(session, supabase);
      } else if (type === 'campaign_application_payment') {
        return await handleApplicationPayment(session, supabase);
      } else if (
        type === 'custom_offer_payment' ||
        type === 'package_order_payment' ||
        type === 'direct_offer_payment'
      ) {
        return await handleOfferPayment(session, supabase);
      } else if (type === 'subscription_payment') {
        return await handleSubscriptionPayment(session, supabase);
      } else {
        console.log('Unknown payment type:', type);
        return NextResponse.json({ received: true });
      }
    }
    // Handle subscription events
    else if (event.type === 'invoice.payment_succeeded') {
      const invoice = event.data.object;
      return await handleSubscriptionRenewal(invoice, supabase);
    } else if (
      event.type === 'customer.subscription.updated' ||
      event.type === 'customer.subscription.deleted'
    ) {
      const subscription = event.data.object;
      return await handleSubscriptionStatusChange(subscription, supabase);
    } else {
      console.log('Unhandled event type:', event.type);
    }

    return NextResponse.json({ received: true });
  } catch (error) {
    console.error('Webhook error:', error);
    return NextResponse.json({ error: 'Webhook error' }, { status: 500 });
  }
}

// Handler functions for different payment types
async function handleFeaturedCampaignPayment(
  session: Stripe.Checkout.Session,
  supabase: SupabaseClient
) {
  const { campaignId, businessId, durationDays } = session.metadata || {};

  if (!campaignId || !businessId || !durationDays) {
    console.error('Missing featured campaign metadata in checkout session');
    return NextResponse.json({ error: 'Missing metadata' }, { status: 400 });
  }

  // Find the price configuration
  const priceConfig = FEATURED_CAMPAIGN_PRICES.find(
    p => p.days === parseInt(durationDays)
  );

  if (!priceConfig) {
    console.error('Invalid duration days:', durationDays);
    return NextResponse.json({ error: 'Invalid duration' }, { status: 400 });
  }

  try {
    // Check if payment already processed (idempotency)
    const { data: existingPayment } = await supabase
      .from('payments')
      .select('id, payment_status')
      .eq('stripe_session_id', session.id)
      .single();

    if (existingPayment) {
      // If payment exists but not completed, update it
      if (existingPayment.payment_status !== 'completed') {
        const featuredUntil = new Date(
          new Date().getTime() + parseInt(durationDays) * 24 * 60 * 60 * 1000
        );

        const { error: updateError } = await supabase
          .from('payments')
          .update({
            payment_status: 'completed',
            payment_completed_at: new Date().toISOString(),
            stripe_payment_intent_id: session.payment_intent,
            featured_until: featuredUntil.toISOString(),
          })
          .eq('id', existingPayment.id);

        if (updateError) {
          console.error('Error updating payment:', updateError);
          return NextResponse.json(
            { error: 'Payment update error' },
            { status: 500 }
          );
        }

        console.log('Payment updated to completed for session:', session.id);
      } else {
        console.log('Payment already processed for session:', session.id);
      }
      return NextResponse.json({ received: true });
    }

    // Calculate featured period end time
    const featuredUntil = new Date(
      new Date().getTime() + parseInt(durationDays) * 24 * 60 * 60 * 1000
    );

    // Calculate fees - no platform fee for featured campaigns
    const paymentAmount = priceConfig.price;
    const platformFee = 0; // Featured campaigns have no platform fee
    const totalPaid = paymentAmount;

    // Create payment record in the payments table
    const { error: paymentError } = await supabase
      .from('payments')
      .insert({
        campaign_id: campaignId, // For featured campaigns, we link to campaign directly
        payment_type: 'featured_campaign',
        payment_amount: paymentAmount,
        platform_fee: platformFee,
        total_paid: totalPaid,
        currency: 'EUR',
        payment_status: 'completed',
        payment_completed_at: new Date().toISOString(),
        stripe_session_id: session.id,
        stripe_payment_intent_id: session.payment_intent,
        featured_until: featuredUntil.toISOString(),
      })
      .select()
      .single();

    if (paymentError) {
      console.error('Error creating payment record:', paymentError);
      return NextResponse.json(
        { error: 'Payment record creation error' },
        { status: 500 }
      );
    }

    // Check if campaign is already featured (idempotency)
    // const { data: _campaign } = await supabase
    //   .from('campaigns')
    //   .select('is_featured, featured_until')
    //   .eq('id', campaignId)
    //   .single();

    const featuredUntilDate = new Date(
      new Date().getTime() + parseInt(durationDays) * 24 * 60 * 60 * 1000
    );

    // Update campaign to be featured (the trigger will handle this automatically, but we do it here too for safety)
    const { error: campaignError } = await supabase
      .from('campaigns')
      .update({
        is_featured: true,
        featured_until: featuredUntilDate.toISOString(),
      })
      .eq('id', campaignId);

    if (campaignError) {
      console.error('Error updating campaign:', campaignError);
      return NextResponse.json(
        { error: 'Campaign update error' },
        { status: 500 }
      );
    }

    console.log(
      `✅ Featured campaign payment processed successfully. Campaign ${campaignId} featured for ${durationDays} days`
    );
    return NextResponse.json({ received: true });
  } catch (error) {
    console.error('Error in handleFeaturedCampaignPayment:', error);
    return NextResponse.json(
      { error: 'Featured campaign payment processing error' },
      { status: 500 }
    );
  }
}

async function handleApplicationPayment(
  session: Stripe.Checkout.Session,
  supabase: SupabaseClient
) {
  const {
    applicationId,
    businessId,
    influencerId,
    campaignId,
    proposedRate,
    platformFee,
    totalAmount,
  } = session.metadata || {};

  if (!applicationId || !businessId || !influencerId) {
    console.error('Missing application payment metadata in checkout session');
    return NextResponse.json({ error: 'Missing metadata' }, { status: 400 });
  }

  try {
    // Check if payment already processed (idempotency)
    const { data: existingPayment } = await supabase
      .from('payments')
      .select('id')
      .eq('stripe_session_id', session.id)
      .single();

    if (existingPayment) {
      console.log('Payment already processed for session:', session.id);
      return NextResponse.json({ received: true });
    }

    // Create payment record in the payments table
    const { error: paymentError } = await supabase
      .from('payments')
      .insert({
        campaign_application_id: applicationId,
        payment_type: 'campaign_application',
        payment_amount: parseFloat(proposedRate),
        platform_fee: parseFloat(platformFee),
        total_paid: parseFloat(totalAmount),
        currency: 'EUR',
        payment_status: 'completed',
        payment_completed_at: new Date().toISOString(),
        stripe_session_id: session.id,
        stripe_payment_intent_id: session.payment_intent,
      })
      .select()
      .single();

    if (paymentError) {
      console.error('Error creating payment record:', paymentError);
      return NextResponse.json(
        { error: 'Payment record creation error' },
        { status: 500 }
      );
    }

    // Update application status
    const { error: updateError } = await supabase
      .from('campaign_applications')
      .update({
        status: 'accepted',
        responded_at: new Date().toISOString(),
      })
      .eq('id', applicationId);

    if (updateError) {
      console.error('Error updating application status:', updateError);
      return NextResponse.json(
        { error: 'Application update error' },
        { status: 500 }
      );
    }

    // Update chat permission - business_approved becomes true after payment
    const { upsertApplicationChatPermission } = await import(
      '@/lib/chat-permissions'
    );
    await upsertApplicationChatPermission(
      businessId,
      influencerId,
      applicationId,
      true, // business_approved - now true after payment
      true // influencer_approved - was already true when applied
    );

    // Send notification to influencer
    try {
      const { data: businessProfile } = await supabase
        .from('profiles')
        .select('username')
        .eq('id', businessId)
        .single();

      const { data: campaignData } = await supabase
        .from('campaigns')
        .select('title')
        .eq('id', campaignId)
        .single();

      if (businessProfile && campaignData) {
        const { notifyPaymentCompletedWorkReady } = await import(
          '@/lib/notifications'
        );
        await notifyPaymentCompletedWorkReady(
          influencerId,
          businessProfile.username,
          campaignData.title,
          'campaign',
          applicationId,
          parseFloat(proposedRate),
          'EUR'
        );
      }
    } catch (notificationError) {
      console.error('Error creating payment notification:', notificationError);
      // Don't fail the payment if notification fails
    }

    console.log('Application payment processed successfully:', applicationId);
    return NextResponse.json({ received: true });
  } catch (error) {
    console.error('Error processing application payment:', error);
    return NextResponse.json(
      { error: 'Payment processing error' },
      { status: 500 }
    );
  }
}

async function handleOfferPayment(
  session: Stripe.Checkout.Session,
  supabase: SupabaseClient
) {
  const {
    offerId,
    businessId,
    influencerId,
    budget,
    platformFee,
    totalAmount,
    type,
  } = session.metadata || {};

  if (!offerId || !businessId || !influencerId) {
    console.error('Missing offer payment metadata in checkout session');
    return NextResponse.json({ error: 'Missing metadata' }, { status: 400 });
  }

  try {
    // Check if payment already processed (idempotency)
    const { data: existingPayment } = await supabase
      .from('payments')
      .select('id')
      .eq('stripe_session_id', session.id)
      .single();

    if (existingPayment) {
      console.log('Payment already processed for session:', session.id);
      return NextResponse.json({ received: true });
    }

    // Determine payment type
    const paymentType =
      type === 'package_order_payment' ? 'package_order' : 'direct_offer';

    // Create payment record in the payments table
    const { error: paymentError } = await supabase
      .from('payments')
      .insert({
        direct_offer_id: offerId,
        payment_type: paymentType,
        payment_amount: parseFloat(budget),
        platform_fee: parseFloat(platformFee),
        total_paid: parseFloat(totalAmount),
        currency: 'EUR',
        payment_status: 'completed',
        payment_completed_at: new Date().toISOString(),
        stripe_session_id: session.id,
        stripe_payment_intent_id: session.payment_intent,
      })
      .select()
      .single();

    if (paymentError) {
      console.error('Error creating payment record:', paymentError);
      return NextResponse.json(
        { error: 'Payment record creation error' },
        { status: 500 }
      );
    }

    // Update offer status to accepted (payment means business accepted)
    const { error: updateError } = await supabase
      .from('direct_offers')
      .update({
        status: 'accepted',
        updated_at: new Date().toISOString(),
      })
      .eq('id', offerId);

    if (updateError) {
      console.error('Error updating offer status:', updateError);
      return NextResponse.json(
        { error: 'Offer update error' },
        { status: 500 }
      );
    }

    // Update chat permission - business_approved becomes true after payment
    const { upsertOfferChatPermission } = await import(
      '@/lib/chat-permissions'
    );
    await upsertOfferChatPermission(
      businessId,
      influencerId,
      offerId,
      true, // business_approved - now true after payment
      true // influencer_approved - was already true when offer was accepted
    );

    // Send notification to influencer
    try {
      const { data: businessProfile } = await supabase
        .from('profiles')
        .select('username')
        .eq('id', businessId)
        .single();

      const { data: offerData } = await supabase
        .from('direct_offers')
        .select('title')
        .eq('id', offerId)
        .single();

      if (businessProfile && offerData) {
        const { notifyPaymentCompletedWorkReady } = await import(
          '@/lib/notifications'
        );
        const projectType =
          type === 'package_order_payment' ? 'package_order' : 'direct_offer';
        await notifyPaymentCompletedWorkReady(
          influencerId,
          businessProfile.username,
          offerData.title,
          projectType,
          offerId,
          parseFloat(budget),
          'EUR'
        );
      }
    } catch (notificationError) {
      console.error('Error creating payment notification:', notificationError);
      // Don't fail the payment if notification fails
    }

    console.log('Offer payment processed successfully:', offerId);
    return NextResponse.json({ received: true });
  } catch (error) {
    console.error('Error processing offer payment:', error);
    return NextResponse.json(
      { error: 'Payment processing error' },
      { status: 500 }
    );
  }
}

// Handler for subscription payments (checkout.session.completed with subscription mode)
async function handleSubscriptionPayment(
  session: Stripe.Checkout.Session,
  supabase: SupabaseClient
) {
  const { planId, userId, userType, planName } = session.metadata || {};

  if (!planId || !userId || !userType) {
    console.error('Missing subscription payment metadata in checkout session');
    return NextResponse.json(
      { error: 'Missing subscription metadata' },
      { status: 400 }
    );
  }

  try {
    // Check if subscription already processed (idempotency)
    const { data: existingSubscription } = await supabase
      .from('user_subscriptions')
      .select('id')
      .eq('stripe_subscription_id', session.subscription)
      .single();

    if (existingSubscription) {
      console.log('Subscription already processed for session:', session.id);
      return NextResponse.json({ received: true });
    }

    // Get subscription details from Stripe
    if (!stripe) {
      throw new Error('Stripe not configured');
    }

    // Get the plan details from database first
    const { data: plan, error: planError } = await supabase
      .from('subscription_plans')
      .select('*')
      .eq('id', planId)
      .single();

    if (planError || !plan) {
      console.error('Plan not found:', planId, planError);
      return NextResponse.json({ error: 'Plan not found' }, { status: 400 });
    }

    // Calculate subscription period based on plan duration (fallback to session created time)
    const sessionCreated = new Date(session.created * 1000);
    const currentPeriodStart = sessionCreated;
    const currentPeriodEnd = new Date(sessionCreated);
    currentPeriodEnd.setMonth(
      currentPeriodEnd.getMonth() + plan.duration_months
    );

    console.log('Calculated period from session:', {
      sessionCreated,
      currentPeriodStart,
      currentPeriodEnd,
      durationMonths: plan.duration_months,
    });

    // Create user subscription record
    const { error: subscriptionError } = await supabase
      .from('user_subscriptions')
      .insert({
        user_id: userId,
        user_type: userType,
        subscription_plan_id: planId,
        stripe_subscription_id: session.subscription,
        status: 'active',
        current_period_start: currentPeriodStart.toISOString(),
        current_period_end: currentPeriodEnd.toISOString(),
        cancel_at_period_end: false,
      })
      .select()
      .single();

    if (subscriptionError) {
      console.error('Error creating subscription record:', subscriptionError);
      return NextResponse.json(
        { error: 'Subscription record creation error' },
        { status: 500 }
      );
    }

    // Create payment record for the initial subscription payment
    const { error: paymentError } = await supabase
      .from('payments')
      .insert({
        user_id: userId,
        user_type: userType,
        payment_type: 'subscription',
        subscription_type: planName,
        subscription_plan: plan.plan_name,
        subscription_duration_months: plan.duration_months,
        subscription_start_date: currentPeriodStart.toISOString(),
        subscription_end_date: currentPeriodEnd.toISOString(),
        payment_amount: plan.price,
        platform_fee: 0, // No platform fee for subscriptions
        total_paid: plan.price,
        currency: 'EUR',
        payment_status: 'completed',
        payment_completed_at: new Date().toISOString(),
        stripe_session_id: session.id,
        stripe_payment_intent_id: session.payment_intent,
      })
      .select()
      .single();

    if (paymentError) {
      console.error('Error creating payment record:', paymentError);
      // Don't fail the subscription if payment record creation fails
    }

    console.log(
      `✅ Subscription payment processed successfully. User ${userId} subscribed to ${plan.plan_name}`
    );
    return NextResponse.json({ received: true });
  } catch (error) {
    console.error('Error processing subscription payment:', error);
    return NextResponse.json(
      { error: 'Subscription payment processing error' },
      { status: 500 }
    );
  }
}

// Handler for subscription renewals (invoice.payment_succeeded)
async function handleSubscriptionRenewal(
  invoice: Stripe.Invoice,
  supabase: SupabaseClient
) {
  try {
    // Skip if it's the first payment (handled by checkout.session.completed)
    if (invoice.billing_reason === 'subscription_create') {
      return NextResponse.json({ received: true });
    }

    const stripeSubscriptionId = invoice.subscription;

    if (!stripeSubscriptionId) {
      console.log('No subscription ID in invoice');
      return NextResponse.json({ received: true });
    }

    // Find the user subscription
    const { data: userSubscription, error: subscriptionError } = await supabase
      .from('user_subscriptions')
      .select(
        `
        *,
        subscription_plans (*)
      `
      )
      .eq('stripe_subscription_id', stripeSubscriptionId)
      .single();

    if (subscriptionError || !userSubscription) {
      console.error(
        'Subscription not found for renewal:',
        stripeSubscriptionId,
        subscriptionError
      );
      return NextResponse.json({ received: true });
    }

    // Get updated subscription details from Stripe
    if (!stripe) {
      throw new Error('Stripe not configured');
    }

    const stripeSubscription =
      await stripe.subscriptions.retrieve(stripeSubscriptionId);
    const currentPeriodStart = new Date(
      stripeSubscription.current_period_start * 1000
    );
    const currentPeriodEnd = new Date(
      stripeSubscription.current_period_end * 1000
    );

    // Update subscription period
    const { error: updateError } = await supabase
      .from('user_subscriptions')
      .update({
        current_period_start: currentPeriodStart.toISOString(),
        current_period_end: currentPeriodEnd.toISOString(),
        status: 'active',
        updated_at: new Date().toISOString(),
      })
      .eq('id', userSubscription.id);

    if (updateError) {
      console.error('Error updating subscription period:', updateError);
      return NextResponse.json(
        { error: 'Subscription update error' },
        { status: 500 }
      );
    }

    // Create payment record for the renewal
    const { error: paymentError } = await supabase.from('payments').insert({
      user_id: userSubscription.user_id,
      user_type: userSubscription.user_type,
      payment_type: 'subscription',
      subscription_type: 'renewal',
      subscription_plan: userSubscription.subscription_plans.plan_name,
      subscription_duration_months:
        userSubscription.subscription_plans.duration_months,
      subscription_start_date: currentPeriodStart.toISOString(),
      subscription_end_date: currentPeriodEnd.toISOString(),
      payment_amount: userSubscription.subscription_plans.price,
      platform_fee: 0,
      total_paid: userSubscription.subscription_plans.price,
      currency: 'EUR',
      payment_status: 'completed',
      payment_completed_at: new Date().toISOString(),
      stripe_payment_intent_id: invoice.payment_intent,
    });

    if (paymentError) {
      console.error('Error creating renewal payment record:', paymentError);
      // Don't fail the renewal if payment record creation fails
    }

    console.log(
      `✅ Subscription renewed successfully. User ${userSubscription.user_id}`
    );
    return NextResponse.json({ received: true });
  } catch (error) {
    console.error('Error processing subscription renewal:', error);
    return NextResponse.json(
      { error: 'Subscription renewal processing error' },
      { status: 500 }
    );
  }
}

// Handler for subscription status changes (customer.subscription.updated/deleted)
async function handleSubscriptionStatusChange(
  subscription: Stripe.Subscription,
  supabase: SupabaseClient
) {
  try {
    const stripeSubscriptionId = subscription.id;

    // Find the user subscription
    const { data: userSubscription, error: subscriptionError } = await supabase
      .from('user_subscriptions')
      .select('*')
      .eq('stripe_subscription_id', stripeSubscriptionId)
      .single();

    if (subscriptionError || !userSubscription) {
      console.error(
        'Subscription not found for status change:',
        stripeSubscriptionId,
        subscriptionError
      );
      return NextResponse.json({ received: true });
    }

    // Map Stripe status to our status
    let status = subscription.status;
    if (subscription.status === 'canceled') {
      status = 'cancelled';
    }

    // Update subscription status
    const { error: updateError } = await supabase
      .from('user_subscriptions')
      .update({
        status: status,
        cancel_at_period_end: subscription.cancel_at_period_end,
        updated_at: new Date().toISOString(),
      })
      .eq('id', userSubscription.id);

    if (updateError) {
      console.error('Error updating subscription status:', updateError);
      return NextResponse.json(
        { error: 'Subscription status update error' },
        { status: 500 }
      );
    }

    console.log(
      `✅ Subscription status updated. User ${userSubscription.user_id} status: ${status}`
    );
    return NextResponse.json({ received: true });
  } catch (error) {
    console.error('Error processing subscription status change:', error);
    return NextResponse.json(
      { error: 'Subscription status change processing error' },
      { status: 500 }
    );
  }
}
