'use client';

import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { getCampaignForEdit, updateCampaign } from '@/lib/campaigns';
import { CreateCampaignForm } from '@/components/campaigns/create-campaign-form';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Loader2 } from 'lucide-react';
import Link from 'next/link';

interface Campaign {
  id: string;
  title: string;
  description: string;
  budget: number | null;
  content_types: string[];
  min_followers?: number;
  max_followers?: number;
  age_range_min?: number;
  age_range_max?: number;
  gender?: string;
  hashtags?: string[] | string | null;
  do_not_mention?: string[] | string | null;
  additional_notes?: string | null;
  collaboration_type?: string;
  location?: string;
  application_deadline?: string;
  payment_terms?: string;
  usage_rights?: string;
  show_business_name?: boolean;
  is_featured?: boolean;
  campaign_platforms: Array<{
    platform_id: string;
    platforms: { name: string };
  }>;
  campaign_categories: Array<{
    category_id: string;
    categories: { name: string };
  }>;
}

export default function EditCampaignPage() {
  const params = useParams();
  const router = useRouter();
  const campaignId = params.id as string;

  const [campaign, setCampaign] = useState<Campaign | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [updating, setUpdating] = useState(false);

  useEffect(() => {
    loadCampaign();
  }, [campaignId]);

  const loadCampaign = async () => {
    try {
      setLoading(true);
      const { data, error } = await getCampaignForEdit(campaignId);

      if (error) {
        setError(error.message || 'Failed to load campaign');
        return;
      }

      if (!data) {
        setError('Campaign not found or not editable');
        return;
      }

      setCampaign(data);
    } catch (err) {
      setError('Failed to load campaign');
      console.error('Error loading campaign:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (formData: any) => {
    try {
      setUpdating(true);

      // Format data properly for updateCampaign function
      const updateData = {
        title: formData.title,
        description: formData.description,
        budget: formData.budget,
        content_types: formData.content_types || [],
        min_followers: formData.min_followers,
        max_followers: formData.max_followers,
        age_range_min: formData.ageRangeMin,
        age_range_max: formData.ageRangeMax,
        gender: formData.gender === 'all' ? null : formData.gender,
        hashtags: formData.hashtags || '',
        doNotMention: formData.doNotMention || '',
        additionalNotes: formData.additionalNotes || '',
        collaborationType: formData.collaborationType || 'paid',
        platforms: formData.selectedPlatforms || [], // Platform IDs
        categories: formData.selectedCategories || [], // Category IDs
      };

      const { error } = await updateCampaign(campaignId, updateData);

      if (error) {
        throw new Error(error.message || 'Failed to update campaign');
      }

      // Redirect to campaign details or dashboard
      router.push(`/campaigns/${campaignId}`);
    } catch (err: any) {
      console.error('Error updating campaign:', err);
      setError(err.message || 'Failed to update campaign');
    } finally {
      setUpdating(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-instagram-subtle relative overflow-hidden flex flex-col">
        {/* Background decorative elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-black/5"></div>
        <div className="absolute top-10 right-10 w-64 h-64 bg-white/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-10 left-10 w-80 h-80 bg-white/5 rounded-full blur-3xl"></div>

        <div className="flex items-center justify-center min-h-screen">
          <div className="flex items-center gap-2 text-white">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span>Učitavam kampanju...</span>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-instagram-subtle relative overflow-hidden flex flex-col">
        {/* Background decorative elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-black/5"></div>
        <div className="absolute top-10 right-10 w-64 h-64 bg-white/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-10 left-10 w-80 h-80 bg-white/5 rounded-full blur-3xl"></div>

        {/* Header */}
        <header className="relative z-10 border-b border-white/20 backdrop-blur-sm bg-white/10">
          <div className="container mx-auto px-4 py-4 flex items-center justify-between">
            <Link
              href="/dashboard/campaigns"
              className="flex items-center space-x-2 text-white/80 hover:text-white transition-colors"
            >
              <ArrowLeft className="h-4 w-4" />
              <span>Nazad na kampanje</span>
            </Link>
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center border border-white/30">
                <span className="text-white font-bold text-lg">🔗</span>
              </div>
              <span className="text-xl font-bold text-white">INFLUEXUS</span>
            </div>
          </div>
        </header>

        <main className="relative z-10 flex-1 px-4 py-8">
          <div className="container mx-auto max-w-2xl">
            <div className="bg-red-500/10 backdrop-blur-sm border border-red-500/20 rounded-lg p-6 text-center">
              <h2 className="text-xl font-semibold text-red-300 mb-2">
                Greška
              </h2>
              <p className="text-red-200 mb-4">{error}</p>
              <Button
                onClick={() => router.push('/dashboard/campaigns')}
                variant="outline"
                className="border-white/30 text-white hover:bg-white/10"
              >
                Idite na kampanje
              </Button>
            </div>
          </div>
        </main>
      </div>
    );
  }

  if (!campaign) {
    return null;
  }

  // Transform campaign data for the form - use whatever fields campaign already has
  const initialData = {
    title: campaign.title,
    description: campaign.description,
    budget: campaign.budget,
    content_types: campaign.content_types,
    min_followers: campaign.min_followers,
    max_followers: campaign.max_followers,
    ageRangeMin: campaign.age_range_min,
    ageRangeMax: campaign.age_range_max,
    gender: campaign.gender,
    selectedCategories:
      campaign.campaign_categories?.map(cc => cc.category_id) || [],
    hashtags: Array.isArray(campaign.hashtags)
      ? campaign.hashtags.join(', ')
      : campaign.hashtags || '',
    doNotMention: Array.isArray(campaign.do_not_mention)
      ? campaign.do_not_mention.join(', ')
      : campaign.do_not_mention || '',
    additionalNotes: campaign.additional_notes || '',
    collaborationType: campaign.collaboration_type || 'paid',
    location: campaign.location || '',
    applicationDeadline: campaign.application_deadline || '',
    paymentTerms: campaign.payment_terms || '',
    usageRights: campaign.usage_rights || '',
    showBusinessName: campaign.show_business_name ?? true,
    isFeatured: campaign.is_featured ?? false,
  };

  return (
    <div className="min-h-screen bg-instagram-subtle relative overflow-hidden flex flex-col">
      {/* Background decorative elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-black/5"></div>
      <div className="absolute top-10 right-10 w-64 h-64 bg-white/5 rounded-full blur-3xl"></div>
      <div className="absolute bottom-10 left-10 w-80 h-80 bg-white/5 rounded-full blur-3xl"></div>

      {/* Header */}
      <header className="relative z-10 border-b border-white/20 backdrop-blur-sm bg-white/10">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <Link
            href="/dashboard/campaigns"
            className="flex items-center space-x-2 text-white/80 hover:text-white transition-colors"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Nazad na kampanje</span>
          </Link>
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center border border-white/30">
              <span className="text-white font-bold text-lg">🔗</span>
            </div>
            <span className="text-xl font-bold text-white">INFLUEXUS</span>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="relative z-10 flex-1 px-4 py-8">
        <div className="container mx-auto max-w-4xl">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-white mb-2">
              Uredi kampanju
            </h1>
            <p className="text-white/80">
              Ažurirajte informacije o vašoj kampanji. Možete uređivati samo
              kampanje u draft statusu.
            </p>
          </div>

          <CreateCampaignForm
            initialData={initialData}
            initialPlatforms={campaign.campaign_platforms}
            onSubmit={handleSubmit}
            isEditing={true}
            isSubmitting={updating}
            submitButtonText={updating ? 'Ažuriram...' : 'Ažuriraj kampanju'}
          />
        </div>
      </main>
    </div>
  );
}
