'use client';

import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Star, ExternalLink } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';
import type { InfluencerSearchResult, SearchFilters } from '@/lib/marketplace';
import { getDisplayName } from '@/lib/utils';

interface InfluencerCardProps {
  influencer: InfluencerSearchResult;
  filters?: SearchFilters;
  disableClick?: boolean;
}

export function InfluencerCard({
  influencer,
  filters,
  disableClick = false,
}: InfluencerCardProps) {
  // formatPrice function is now imported from @/lib/currency

  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;
    const starSize = 'w-2.5 h-2.5 md:w-3 md:h-3';

    for (let i = 0; i < 5; i++) {
      if (i < fullStars) {
        stars.push(
          <Star
            key={i}
            className={`${starSize} fill-amber-400 text-amber-400`}
          />
        );
      } else if (i === fullStars && hasHalfStar) {
        stars.push(
          <div key={i} className={`relative ${starSize}`}>
            <Star className={`${starSize} text-gray-300 absolute`} />
            <Star
              className={`${starSize} fill-amber-400 text-amber-400 absolute overflow-hidden`}
              style={{ clipPath: 'inset(0 50% 0 0)' }}
            />
          </div>
        );
      } else {
        stars.push(<Star key={i} className={`${starSize} text-gray-300`} />);
      }
    }

    return stars;
  };

  const formatFollowers = (count: number) => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  };

  // Rezervisano mjesto - uvijek 3 platforme
  const displayPlatforms = [
    ...influencer.platforms.slice(0, 3),
    ...Array(Math.max(0, 3 - influencer.platforms.length)).fill(null),
  ];

  // Glavna kategorija za prikaz
  const mainCategory = influencer.categories?.[0]?.name || 'Influencer';

  const cardContent = (
    <div
      className={`p-1 bg-gradient-to-br from-pink-500 via-purple-500 to-orange-400 rounded-xl group transition-all duration-200 ${!disableClick ? 'hover:shadow-lg cursor-pointer' : 'cursor-default'}`}
    >
      <Card className="w-full overflow-hidden bg-white h-full py-0">
        <CardContent className="p-0">
          {/* Image section */}
          <div className="relative w-full aspect-square overflow-hidden bg-gray-100">
            <Image
              src={influencer.avatar_url || '/placeholder.svg'}
              alt={`${getDisplayName(influencer) !== 'Ime i prezime skriveno' ? getDisplayName(influencer) : influencer.username} profile`}
              fill
              quality={100}
              className="object-cover"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            />

            {/* Verified badge - top left */}
            {influencer.subscription_type === 'premium' && (
              <div className="absolute top-2 left-2 md:top-2.5 md:left-2.5 z-20">
                <Badge
                  variant="secondary"
                  className="bg-gradient-to-r from-pink-500 to-purple-500 text-white border-0 px-1.5 py-0.5 md:px-1.5 md:py-0.5 shadow-lg"
                >
                  <Image
                    src="/images/influexus_logo_white_small_300x300.webp"
                    alt="Verified"
                    width={10}
                    height={10}
                    className="w-2.5 h-2.5 md:w-2.5 md:h-2.5 mr-0.5"
                  />
                  <span className="text-xs">Verified</span>
                </Badge>
              </div>
            )}

            {/* Star rating - top right */}
            <div className="absolute top-2 right-2 md:top-2.5 md:right-2.5 z-20">
              <div className="bg-white/95 backdrop-blur-sm rounded-full px-2 py-1 md:px-2.5 md:py-1 flex items-center gap-1 md:gap-1 shadow-md border border-white/20">
                <div className="flex items-center gap-0.5">
                  {renderStars(influencer.average_rating || 0)}
                </div>
                <span className="text-gray-900 text-xs md:text-xs font-medium">
                  ({influencer.total_reviews || 0})
                </span>
              </div>
            </div>

            {/* Username - bottom left */}
            <div className="absolute bottom-2 left-2 md:bottom-2.5 md:left-2.5 z-20">
              <div
                className="bg-black/70 backdrop-blur-sm rounded-md px-1.5 py-0.5 border-0"
                style={{
                  backgroundColor: 'rgba(0, 0, 0, 0.7)',
                  boxShadow: 'none',
                }}
              >
                <h3 className="text-white font-medium text-xs">
                  @{influencer.username}
                </h3>
              </div>
            </div>

            {/* Total followers - bottom right */}
            <div className="absolute bottom-2 right-2 md:bottom-2.5 md:right-2.5 z-20">
              <div
                className="bg-black/70 backdrop-blur-sm rounded-md px-1.5 py-0.5 border-0 inline-flex items-center"
                style={{
                  backgroundColor: 'rgba(0, 0, 0, 0.7)',
                  boxShadow: 'none',
                }}
              >
                <span className="text-white font-medium text-xs">
                  {formatFollowers(influencer.total_followers)}
                </span>
              </div>
            </div>
          </div>

          {/* Info section */}
          <div className="p-3 space-y-2">
            {/* Location */}
            {(influencer.city || influencer.country) && (
              <div className="text-left">
                <p className="text-xs text-gray-500">
                  {influencer.city && influencer.country
                    ? `${influencer.city}, ${influencer.country}`
                    : influencer.city || influencer.country}
                </p>
              </div>
            )}

            {/* Bio - rezervisano mjesto za 2 reda */}
            <div className="h-8 flex items-start">
              <p className="text-xs text-gray-600 line-clamp-2 leading-4">
                {influencer.bio || ''}
              </p>
            </div>

            {/* Separator */}
            <hr className="border-gray-200" />

            {/* View Packages Button */}
            <div className="pt-1.5">
              <Button className="w-full bg-gradient-to-r from-pink-500 to-purple-500 hover:from-pink-600 hover:to-purple-600 text-white shadow-lg hover:shadow-xl transition-all duration-300 group h-9 rounded-xl text-sm">
                <span className="font-medium">Pogledaj pakete</span>
                <ExternalLink className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-200" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const handleInfluencerClick = () => {
    // Track that we're navigating to an influencer profile
    const targetPage = `/influencer/${influencer.username}`;
    sessionStorage.setItem('lastVisitedPage', targetPage);

    // Explicitly save current scroll position
    const scrollTop = window.scrollY || window.pageYOffset;
    sessionStorage.setItem(
      'scroll-marketplace-influencers',
      scrollTop.toString()
    );
  };

  return disableClick ? (
    cardContent
  ) : (
    <Link
      href={`/influencer/${influencer.username}`}
      onClick={handleInfluencerClick}
    >
      {cardContent}
    </Link>
  );
}
