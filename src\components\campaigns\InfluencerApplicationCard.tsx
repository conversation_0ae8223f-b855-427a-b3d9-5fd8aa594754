import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Euro,
  Eye,
  Clock,
  CheckCircle,
  XCircle,
  FileText,
  X,
  Loader2,
} from 'lucide-react';
import Link from 'next/link';
import { formatDate } from '@/lib/date-utils';

interface Application {
  id: string;
  campaign_id: string;
  status: 'pending' | 'accepted' | 'rejected';
  proposed_rate: string;
  proposal_text: string;
  delivery_timeframe: string;
  portfolio_links: string[] | null;
  experience_relevant: string | null;
  audience_insights: string | null;
  applied_at: string;
  campaign_title: string;
  campaign_budget: number;
  campaign_business_id: string;
  business_username: string;
}

interface InfluencerApplicationCardProps {
  application: Application;
  onWithdraw?: (applicationId: string) => void;
  isWithdrawing?: boolean;
}

const InfluencerApplicationCard: React.FC<InfluencerApplicationCardProps> = ({
  application,
  onWithdraw,
  isWithdrawing = false,
}) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-gradient-to-r from-yellow-100 to-orange-100 text-yellow-800 border-yellow-200';
      case 'accepted':
        return 'bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 border-green-200';
      case 'rejected':
        return 'bg-gradient-to-r from-red-100 to-pink-100 text-red-800 border-red-200';
      default:
        return 'bg-gradient-to-r from-gray-100 to-slate-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4" />;
      case 'accepted':
        return <CheckCircle className="h-4 w-4" />;
      case 'rejected':
        return <XCircle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Na čekanju';
      case 'accepted':
        return 'Prihvaćeno';
      case 'rejected':
        return 'Odbačeno';
      default:
        return status;
    }
  };

  return (
    <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300 group hover:scale-[1.02] h-[380px] flex flex-col">
      {/* Dreamy gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60 group-hover:opacity-80 transition-opacity duration-300" />

      <div className="relative p-5 flex flex-col h-full">
        {/* Header with campaign title and status */}
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1 pr-3 h-10">
            <h3 className="text-lg font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent line-clamp-2 leading-5">
              {application.campaign_title}
            </h3>
          </div>

          <Badge
            variant="outline"
            className={`${getStatusColor(application.status)} font-medium flex-shrink-0 text-xs`}
          >
            <div className="flex items-center space-x-1">
              {getStatusIcon(application.status)}
              <span className="hidden sm:inline">
                {getStatusText(application.status)}
              </span>
            </div>
          </Badge>
        </div>

        {/* Budget info - kompaktno */}
        <div className="bg-gradient-to-br from-white/30 via-purple-50/40 to-pink-50/30 dark:from-gray-800/30 dark:via-purple-900/20 dark:to-pink-900/20 rounded-lg p-2 border border-purple-200/30 dark:border-purple-700/30 backdrop-blur-sm mb-3">
          <div className="flex items-center justify-between">
            <span className="text-xs text-gray-600 dark:text-gray-400">
              Budžet:
            </span>
            <span className="font-medium text-sm text-gray-900 dark:text-gray-100">
              {application.campaign_budget?.toLocaleString('de-DE', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2,
              }) || 'N/A'}{' '}
              €
            </span>
          </div>
        </div>

        {/* Application Details - kao budžet */}
        <div className="space-y-2 mb-3">
          {/* Vaša cijena */}
          <div className="bg-gradient-to-br from-white/30 via-purple-50/40 to-pink-50/30 dark:from-gray-800/30 dark:via-purple-900/20 dark:to-pink-900/20 rounded-lg p-2 border border-purple-200/30 dark:border-purple-700/30 backdrop-blur-sm">
            <div className="flex items-center justify-between">
              <span className="text-xs text-gray-600 dark:text-gray-400">
                Vaša cijena:
              </span>
              <span className="font-medium text-sm text-gray-900 dark:text-gray-100">
                {parseFloat(application.proposed_rate).toLocaleString('de-DE', {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                })}{' '}
                €
              </span>
            </div>
          </div>

          {/* Potrebno vrijeme */}
          <div className="bg-gradient-to-br from-white/30 via-purple-50/40 to-pink-50/30 dark:from-gray-800/30 dark:via-purple-900/20 dark:to-pink-900/20 rounded-lg p-2 border border-purple-200/30 dark:border-purple-700/30 backdrop-blur-sm">
            <div className="flex items-center justify-between">
              <span className="text-xs text-gray-600 dark:text-gray-400">
                Potrebno vrijeme:
              </span>
              <span className="font-medium text-sm text-gray-900 dark:text-gray-100">
                {application.delivery_timeframe}
              </span>
            </div>
          </div>
        </div>

        {/* Proposal preview - povećano za jedan red */}
        <div className="flex-1 mb-2">
          {application.proposal_text ? (
            <div className="bg-gradient-to-br from-white/30 via-purple-50/40 to-pink-50/30 dark:from-gray-800/30 dark:via-purple-900/20 dark:to-pink-900/20 rounded-lg p-3 border border-purple-200/30 dark:border-purple-700/30 backdrop-blur-sm h-24 flex flex-col">
              <div className="flex items-center gap-1 mb-1">
                <FileText className="w-3 h-3 text-purple-600 flex-shrink-0" />
                <h4 className="text-xs font-medium text-gray-800 dark:text-gray-200">
                  Prijedlog:
                </h4>
              </div>
              <p className="text-xs text-gray-700 dark:text-gray-300 line-clamp-5 flex-1 leading-relaxed">
                {application.proposal_text}
              </p>
            </div>
          ) : (
            <div className="bg-gradient-to-br from-white/30 via-purple-50/40 to-pink-50/30 dark:from-gray-800/30 dark:via-purple-900/20 dark:to-pink-900/20 rounded-lg p-3 border border-purple-200/30 dark:border-purple-700/30 backdrop-blur-sm h-24 flex items-center justify-center">
              <p className="text-xs text-gray-500 italic">Nema prijedloga</p>
            </div>
          )}
        </div>

        {/* Action Buttons - Push to bottom */}
        <div className="flex gap-2 pt-2 mt-auto">
          <Link
            href={`/dashboard/influencer/applications/${application.id}`}
            className="flex-1"
          >
            <button className="flex items-center justify-center gap-1 w-full px-2 py-1.5 text-xs font-medium text-white bg-gradient-to-r from-purple-400 via-pink-400 to-purple-500 hover:from-purple-500 hover:via-pink-500 hover:to-purple-600 rounded-lg transition-all duration-200 hover:shadow-lg hover:shadow-purple-200/50 dark:hover:shadow-purple-900/30">
              <Eye className="w-3 h-3" />
              <span className="hidden sm:inline">Detaljan pregled</span>
              <span className="sm:hidden">Pregled</span>
            </button>
          </Link>

          {/* Withdraw button - only show if status is pending and onWithdraw is provided */}
          {application.status === 'pending' && onWithdraw && (
            <Button
              size="sm"
              variant="outline"
              className="border-red-200 text-red-600 hover:bg-red-50 hover:border-red-300 hover:text-red-700 dark:border-red-800 dark:text-red-400 dark:hover:bg-red-950/20 px-2 py-1.5"
              onClick={() => onWithdraw(application.id)}
              disabled={isWithdrawing}
              title="Povuci aplikaciju"
            >
              {isWithdrawing ? (
                <Loader2 className="h-3 w-3 animate-spin" />
              ) : (
                <X className="h-3 w-3" />
              )}
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export default InfluencerApplicationCard;
