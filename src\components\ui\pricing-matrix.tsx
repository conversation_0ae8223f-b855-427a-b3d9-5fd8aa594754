'use client';

import { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { supabase } from '@/lib/supabase';
import PlatformIconSimple from '@/components/ui/platform-icon-simple';

interface Platform {
  id: number;
  name: string;
  slug: string;
  icon: string;
}

interface ContentType {
  id: number;
  name: string;
  slug: string;
  description: string;
}

interface SelectedPlatform {
  platform_id: number;
  handle: string;
  followers_count: number;
  content_type_ids: number[];
}

interface PricingEntry {
  platform_id: number;
  content_type_id: number;
  price: number;
  is_available: boolean;
}

interface PricingMatrixProps {
  selectedPlatforms: SelectedPlatform[];
  pricing: PricingEntry[];
  onPricingChange: (pricing: PricingEntry[]) => void;
  className?: string;
  disabled?: boolean;
}

export function PricingMatrix({
  selectedPlatforms,
  pricing,
  onPricingChange,
  className,
  disabled = false,
}: PricingMatrixProps) {
  const [platforms, setPlatforms] = useState<Platform[]>([]);
  const [contentTypes, setContentTypes] = useState<ContentType[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadPlatformsAndContentTypes();
  }, []);

  const loadPlatformsAndContentTypes = async () => {
    try {
      setLoading(true);

      // Load platforms
      const { data: platformsData, error: platformsError } = await supabase
        .from('platforms')
        .select('*')
        .eq('is_active', true)
        .order('name');

      if (platformsError) {
        console.error('Error loading platforms:', platformsError);
        return;
      }

      // Load content types
      const { data: contentTypesData, error: contentTypesError } =
        await supabase
          .from('content_types')
          .select('*')
          .eq('is_active', true)
          .order('name');

      if (contentTypesError) {
        console.error('Error loading content types:', contentTypesError);
        return;
      }

      setPlatforms(platformsData || []);
      setContentTypes(contentTypesData || []);
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getPlatformName = (platformId: number) => {
    return platforms.find(p => p.id === platformId)?.name || '';
  };

  const getPlatformIcon = (platformId: number) => {
    return platforms.find(p => p.id === platformId)?.icon || '';
  };

  const getContentTypeName = (contentTypeId: number) => {
    return contentTypes.find(ct => ct.id === contentTypeId)?.name || '';
  };

  const getPrice = (platformId: number, contentTypeId: number) => {
    const entry = pricing.find(
      p => p.platform_id === platformId && p.content_type_id === contentTypeId
    );
    return entry?.price || 0;
  };

  const isAvailable = (platformId: number, contentTypeId: number) => {
    const entry = pricing.find(
      p => p.platform_id === platformId && p.content_type_id === contentTypeId
    );
    return entry?.is_available !== false;
  };

  const updatePrice = (
    platformId: number,
    contentTypeId: number,
    price: number
  ) => {
    const existingIndex = pricing.findIndex(
      p => p.platform_id === platformId && p.content_type_id === contentTypeId
    );

    const newPricing = [...pricing];

    if (existingIndex >= 0) {
      newPricing[existingIndex] = {
        ...newPricing[existingIndex],
        price: price,
      };
    } else {
      newPricing.push({
        platform_id: platformId,
        content_type_id: contentTypeId,
        price: price,
        is_available: true,
      });
    }

    onPricingChange(newPricing);
  };

  const toggleAvailability = (platformId: number, contentTypeId: number) => {
    const existingIndex = pricing.findIndex(
      p => p.platform_id === platformId && p.content_type_id === contentTypeId
    );

    const newPricing = [...pricing];

    if (existingIndex >= 0) {
      newPricing[existingIndex] = {
        ...newPricing[existingIndex],
        is_available: !newPricing[existingIndex].is_available,
      };
    } else {
      newPricing.push({
        platform_id: platformId,
        content_type_id: contentTypeId,
        price: 0,
        is_available: false,
      });
    }

    onPricingChange(newPricing);
  };

  // Get all platform/content combinations that should be shown
  const getPricingCombinations = () => {
    const combinations: Array<{
      platform_id: number;
      content_type_id: number;
      platform_name: string;
      platform_icon: string;
      content_type_name: string;
    }> = [];

    selectedPlatforms.forEach(platform => {
      platform.content_type_ids.forEach(contentTypeId => {
        combinations.push({
          platform_id: platform.platform_id,
          content_type_id: contentTypeId,
          platform_name: getPlatformName(platform.platform_id),
          platform_icon: getPlatformIcon(platform.platform_id),
          content_type_name: getContentTypeName(contentTypeId),
        });
      });
    });

    return combinations;
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="h-10 w-full bg-muted animate-pulse rounded-md"></div>
        <div className="h-32 w-full bg-muted animate-pulse rounded-md"></div>
      </div>
    );
  }

  const combinations = getPricingCombinations();

  if (combinations.length === 0) {
    return (
      <div className={cn('space-y-4', className)}>
        <Label className="text-base font-medium">Cijene</Label>
        <Card>
          <CardContent className="py-8 text-center">
            <p className="text-muted-foreground">
              Prvo izaberite platforme i content tipove da biste postavili
              cijene
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className={cn('space-y-4', className)}>
      <div>
        <Label className="text-base font-medium">Cijene</Label>
        <p className="text-sm text-muted-foreground">
          Postavite cijene za svaki content tip koji nudite
        </p>
      </div>

      <div className="space-y-3">
        {combinations.map(
          ({
            platform_id,
            content_type_id,
            platform_name,
            platform_icon,
            content_type_name,
          }) => (
            <Card key={`${platform_id}-${content_type_id}`}>
              <CardContent className="p-4">
                <div className="flex items-center justify-between gap-4">
                  <div className="flex items-center gap-3 flex-1">
                    <PlatformIconSimple platform={platform_name} size="lg" />
                    <div className="flex-1">
                      <div className="font-medium text-sm">
                        {platform_name} • {content_type_name}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        Cijena za jedan {content_type_name.toLowerCase()} na{' '}
                        {platform_name}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-3">
                    <div className="flex items-center gap-2">
                      <Input
                        type="number"
                        placeholder="0"
                        value={getPrice(platform_id, content_type_id) || ''}
                        onChange={e =>
                          updatePrice(
                            platform_id,
                            content_type_id,
                            parseFloat(e.target.value) || 0
                          )
                        }
                        disabled={
                          disabled || !isAvailable(platform_id, content_type_id)
                        }
                        className="w-24 text-right"
                        min="0"
                        step="0.01"
                      />
                      <span className="text-sm text-muted-foreground">KM</span>
                    </div>

                    <Badge
                      variant={
                        isAvailable(platform_id, content_type_id)
                          ? 'default'
                          : 'secondary'
                      }
                      className={cn(
                        'cursor-pointer transition-colors',
                        !disabled && 'hover:opacity-80'
                      )}
                      onClick={() =>
                        !disabled &&
                        toggleAvailability(platform_id, content_type_id)
                      }
                    >
                      {isAvailable(platform_id, content_type_id)
                        ? 'Dostupno'
                        : 'Nedostupno'}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        )}
      </div>

      {/* Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Pregled cijena</CardTitle>
          <CardDescription>
            Ukupno {combinations.length} content tipova na{' '}
            {selectedPlatforms.length} platformi
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {combinations
              .filter(
                ({ platform_id, content_type_id }) =>
                  isAvailable(platform_id, content_type_id) &&
                  getPrice(platform_id, content_type_id) > 0
              )
              .map(
                ({
                  platform_id,
                  content_type_id,
                  platform_name,
                  platform_icon,
                  content_type_name,
                }) => (
                  <div
                    key={`summary-${platform_id}-${content_type_id}`}
                    className="flex items-center justify-between p-3 bg-muted/50 rounded-lg"
                  >
                    <div className="flex items-center gap-2">
                      <PlatformIconSimple platform={platform_name} size="sm" />
                      <span className="text-sm font-medium">
                        {content_type_name}
                      </span>
                    </div>
                    <span className="text-sm font-bold">
                      {getPrice(platform_id, content_type_id)} KM
                    </span>
                  </div>
                )
              )}
          </div>

          {combinations.filter(
            ({ platform_id, content_type_id }) =>
              isAvailable(platform_id, content_type_id) &&
              getPrice(platform_id, content_type_id) > 0
          ).length === 0 && (
            <p className="text-center text-muted-foreground py-4">
              Postavite cijene za content tipove da biste videli pregled
            </p>
          )}
        </CardContent>
      </Card>

      {/* Helper Text */}
      <div className="text-xs text-muted-foreground">
        <p>• Postavite cijene u KM (konvertibilnim markama)</p>
        <p>• Kliknite na "Dostupno/Nedostupno" da promijenite status</p>
        <p>
          • Samo dostupni content tipovi će biti vidljivi biznis korisnicima
        </p>
      </div>
    </div>
  );
}
