import { NextRequest, NextResponse } from 'next/server';
import {
  validateJ<PERSON>TToken,
  applyRateLimit,
  getCORSHeaders,
  validateRequestBody,
  AuthValidationOptions,
} from './api-auth';

export interface ApiMiddlewareOptions extends AuthValidationOptions {
  // Rate limiting options
  rateLimit?: {
    enabled?: boolean;
    strict?: boolean;
    customLimit?: { maxRequests: number; windowMs: number };
  };

  // CORS options
  cors?: {
    enabled?: boolean;
    allowedOrigins?: string[];
  };

  // Request validation options
  validation?: {
    enabled?: boolean;
    bodySchema?: Record<string, unknown>;
    validateContentType?: boolean;
  };

  // Security headers
  securityHeaders?: boolean;
}

export interface ApiContext {
  user?: {
    id: string;
    email: string;
    user_type: 'influencer' | 'business';
    profile_completed?: boolean;
    metadata?: Record<string, unknown>;
  };
  request: NextRequest;
}

export type ApiHandler = (
  context: ApiContext,
  response?: NextResponse
) => Promise<NextResponse> | NextResponse;

/**
 * Comprehensive API middleware that handles authentication, rate limiting, CORS, and validation
 */
export function withApiMiddleware(
  handler: ApiHandler,
  options: ApiMiddlewareOptions = {}
) {
  return async function middlewareWrapper(
    request: NextRequest
  ): Promise<NextResponse> {
    const startTime = Date.now();

    // Initialize response with CORS headers if enabled
    let response: NextResponse | undefined;
    const origin = request.headers.get('origin');

    // Handle preflight OPTIONS request
    if (request.method === 'OPTIONS') {
      if (options.cors?.enabled !== false) {
        const corsHeaders = getCORSHeaders(origin);
        return new NextResponse(null, {
          status: 200,
          headers: corsHeaders,
        });
      }
      return new NextResponse(null, { status: 200 });
    }

    try {
      // 1. Apply rate limiting
      if (options.rateLimit?.enabled !== false) {
        const rateLimitResult = applyRateLimit(request, {
          strict: options.rateLimit?.strict,
          customLimit: options.rateLimit?.customLimit,
        });

        if (!rateLimitResult.allowed) {
          const headers: Record<string, string> = {
            'X-RateLimit-Limit':
              options.rateLimit?.customLimit?.maxRequests?.toString() || '100',
            'X-RateLimit-Remaining': rateLimitResult.remaining.toString(),
            'X-RateLimit-Reset': new Date(
              rateLimitResult.resetTime
            ).toISOString(),
            'Retry-After': Math.ceil(
              (rateLimitResult.resetTime - Date.now()) / 1000
            ).toString(),
          };

          if (options.cors?.enabled !== false) {
            Object.assign(headers, getCORSHeaders(origin));
          }

          return NextResponse.json(
            {
              error: 'Too many requests',
              message: 'Rate limit exceeded. Please try again later.',
              retryAfter: Math.ceil(
                (rateLimitResult.resetTime - Date.now()) / 1000
              ),
            },
            {
              status: 429,
              headers,
            }
          );
        }

        // Add rate limit headers to successful responses
        response = NextResponse.json({}, { status: 200 });
        response.headers.set(
          'X-RateLimit-Limit',
          options.rateLimit?.customLimit?.maxRequests?.toString() || '100'
        );
        response.headers.set(
          'X-RateLimit-Remaining',
          rateLimitResult.remaining.toString()
        );
        response.headers.set(
          'X-RateLimit-Reset',
          new Date(rateLimitResult.resetTime).toISOString()
        );
      }

      // 2. Validate authentication
      const authResult = await validateJWTToken(request, options);

      if (!authResult.success) {
        const headers: Record<string, string> = {};

        if (options.cors?.enabled !== false) {
          Object.assign(headers, getCORSHeaders(origin));
        }

        return NextResponse.json(
          {
            error: 'Authentication failed',
            message: authResult.error || 'Invalid or missing authentication',
          },
          {
            status: authResult.statusCode || 401,
            headers,
          }
        );
      }

      // 3. Validate request body if POST/PUT/PATCH
      if (
        ['POST', 'PUT', 'PATCH'].includes(request.method) &&
        options.validation?.enabled !== false
      ) {
        // Validate Content-Type
        if (options.validation?.validateContentType !== false) {
          const contentType = request.headers.get('content-type');
          if (!contentType?.includes('application/json')) {
            const headers: Record<string, string> = {};
            if (options.cors?.enabled !== false) {
              Object.assign(headers, getCORSHeaders(origin));
            }

            return NextResponse.json(
              {
                error: 'Invalid Content-Type',
                message: 'Expected application/json',
              },
              {
                status: 400,
                headers,
              }
            );
          }
        }

        // Validate request body against schema
        if (options.validation?.bodySchema) {
          try {
            const body = await request.json();
            const validationResult = validateRequestBody(
              body,
              options.validation.bodySchema
            );

            if (!validationResult.valid) {
              const headers: Record<string, string> = {};
              if (options.cors?.enabled !== false) {
                Object.assign(headers, getCORSHeaders(origin));
              }

              return NextResponse.json(
                {
                  error: 'Validation failed',
                  message: 'Request body validation failed',
                  details: validationResult.errors,
                },
                {
                  status: 400,
                  headers,
                }
              );
            }

            // Create a new request with the parsed body for consistency
            const newRequest = new NextRequest(request.url, {
              method: request.method,
              headers: request.headers,
              body: JSON.stringify(body),
            });

            request = newRequest;
          } catch (error) {
            const headers: Record<string, string> = {};
            if (options.cors?.enabled !== false) {
              Object.assign(headers, getCORSHeaders(origin));
            }

            return NextResponse.json(
              {
                error: 'Invalid JSON',
                message: 'Request body must be valid JSON',
              },
              {
                status: 400,
                headers,
              }
            );
          }
        }
      }

      // 4. Create context and call the handler
      const context: ApiContext = {
        user: authResult.user,
        request,
      };

      const handlerResponse = await handler(context, response);

      // 5. Apply security headers and CORS to the response
      if (options.cors?.enabled !== false) {
        const corsHeaders = getCORSHeaders(origin);
        Object.entries(corsHeaders).forEach(([key, value]) => {
          handlerResponse.headers.set(key, value);
        });
      }

      if (options.securityHeaders !== false) {
        // Apply security headers
        handlerResponse.headers.set('X-Content-Type-Options', 'nosniff');
        handlerResponse.headers.set('X-Frame-Options', 'DENY');
        handlerResponse.headers.set('X-XSS-Protection', '1; mode=block');
        handlerResponse.headers.set(
          'Referrer-Policy',
          'strict-origin-when-cross-origin'
        );
        handlerResponse.headers.set(
          'Content-Security-Policy',
          "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline'; style-src 'self' 'unsafe-inline';"
        );
      }

      // Add processing time header
      const processingTime = Date.now() - startTime;
      handlerResponse.headers.set('X-Response-Time', `${processingTime}ms`);

      return handlerResponse;
    } catch (error) {
      console.error('API middleware error:', error);

      const headers: Record<string, string> = {};
      if (options.cors?.enabled !== false) {
        Object.assign(headers, getCORSHeaders(origin));
      }

      return NextResponse.json(
        {
          error: 'Internal server error',
          message: 'An unexpected error occurred',
        },
        {
          status: 500,
          headers,
        }
      );
    }
  };
}

/**
 * Convenience wrapper for webhook endpoints
 */
export function withWebhookMiddleware(
  handler: ApiHandler,
  webhookSecret: string,
  options: Omit<ApiMiddlewareOptions, 'requireAuth' | 'webhookSecret'> = {}
) {
  return withApiMiddleware(handler, {
    ...options,
    requireAuth: false,
    webhookSecret,
    rateLimit: {
      strict: true,
      customLimit: { maxRequests: 50, windowMs: 60 * 1000 }, // 50 requests per minute for webhooks
      ...options.rateLimit,
    },
  });
}

/**
 * Convenience wrapper for public endpoints (no auth required)
 */
export function withPublicMiddleware(
  handler: ApiHandler,
  options: Omit<ApiMiddlewareOptions, 'requireAuth'> = {}
) {
  return withApiMiddleware(handler, {
    ...options,
    requireAuth: false,
  });
}

/**
 * Convenience wrapper for protected endpoints (auth required)
 */
export function withProtectedMiddleware(
  handler: ApiHandler,
  options: ApiMiddlewareOptions = {}
) {
  return withApiMiddleware(handler, {
    ...options,
    requireAuth: true,
  });
}
