import { supabase } from './supabase';
import { DEFAULT_CURRENCY } from './currency';

// Types
export interface Platform {
  id: number;
  name: string;
  slug: string;
  icon: string;
  is_active: boolean;
}

export interface ContentType {
  id: number;
  platform_id: number;
  name: string;
  slug: string;
  description: string;
  is_active: boolean;
}

export interface PricingPackage {
  id: number;
  influencer_id: string;
  platform_id: number;
  content_type_id: number;
  quantity: number;
  video_duration?: string;
  price: number;
  currency: string;
  auto_generated_name: string;
  is_available: boolean;
  created_at: string;
  updated_at: string;
  // Joined data
  platform_name?: string;
  platform_icon?: string;
  content_type_name?: string;
}

export interface PackageCreationData {
  platform_id: number;
  content_type_id: number;
  quantity: number;
  video_duration?: string;
  price: number;
}

// Database query result types
interface PricingPackageQueryResult {
  id: number;
  influencer_id: string;
  platform_id: number;
  content_type_id: number;
  quantity: number | null;
  video_duration: string | null;
  price: number;
  currency: string | null;
  auto_generated_name: string | null;
  is_available: boolean | null;
  created_at: string;
  updated_at: string;
  platforms: {
    name: string;
    icon: string;
  };
  content_types: {
    name: string;
  };
}

// Video duration options
export const VIDEO_DURATIONS = [
  { value: '30s', label: '30 sekundi' },
  { value: '1min', label: '1 minuta' },
  { value: '3min', label: '3 minute' },
  { value: '5min', label: '5 minuta' },
  { value: '10min', label: '10 minuta' },
  { value: '15min', label: '15 minuta' },
  { value: '30min', label: '30 minuta' },
];

/**
 * Generate package name based on platform, content type, quantity and duration
 */
export function generatePackageName(
  quantity: number,
  platformName: string,
  contentTypeName: string,
  videoDuration?: string
): string {
  let name = `${quantity}x ${platformName} ${contentTypeName}`;

  if (videoDuration && contentTypeName.toLowerCase().includes('video')) {
    name += ` ${videoDuration}`;
  }

  return name;
}

/**
 * Get all active platforms
 */
export async function getPlatforms() {
  const { data, error } = await supabase
    .from('platforms')
    .select('*')
    .eq('is_active', true)
    .order('name');

  return { data, error };
}

/**
 * Get all active content types
 */
export async function getContentTypes() {
  const { data, error } = await supabase
    .from('content_types')
    .select('*')
    .eq('is_active', true)
    .order('platform_id, name');

  return { data, error };
}

/**
 * Get content types for specific platform
 */
export async function getContentTypesByPlatform(platformId: number) {
  const { data, error } = await supabase
    .from('content_types')
    .select('*')
    .eq('platform_id', platformId)
    .eq('is_active', true)
    .order('name');

  return { data, error };
}

/**
 * Get platforms with their content types
 */
export async function getPlatformsWithContentTypes() {
  const { data, error } = await supabase.rpc(
    'get_platforms_with_content_types'
  );

  return { data, error };
}

/**
 * Get influencer's pricing packages
 */
export async function getInfluencerPackages(influencerId: string) {
  const { data, error } = await supabase
    .from('influencer_platform_pricing')
    .select(
      `
      *,
      platforms!inner(name, icon),
      content_types!inner(name)
    `
    )
    .eq('influencer_id', influencerId)
    .eq('is_available', true)
    .order('created_at', { ascending: false });

  if (error) return { data: null, error };

  // Transform data to include platform and content type info
  const packages: PricingPackage[] = data.map(
    (item: PricingPackageQueryResult) => ({
      id: item.id,
      influencer_id: item.influencer_id,
      platform_id: item.platform_id,
      content_type_id: item.content_type_id,
      quantity: item.quantity || 1,
      video_duration: item.video_duration,
      price: item.price,
      currency: item.currency || 'KM',
      auto_generated_name: item.auto_generated_name,
      is_available: item.is_available,
      created_at: item.created_at,
      updated_at: item.updated_at,
      platform_name: item.platforms.name,
      platform_icon: item.platforms.icon,
      content_type_name: item.content_types.name,
    })
  );

  return { data: packages, error: null };
}

/**
 * Create new pricing package
 */
export async function createPricingPackage(
  influencerId: string,
  packageData: PackageCreationData
) {
  try {
    console.log('Creating package with data:', { influencerId, packageData });

    // First, get platform and content type info to generate name
    const { data: platform, error: platformError } = await supabase
      .from('platforms')
      .select('name')
      .eq('id', packageData.platform_id)
      .single();

    if (platformError) {
      console.error('Error fetching platform:', platformError);
      return {
        data: null,
        error: {
          message: 'Greška pri dohvaćanju platforme: ' + platformError.message,
        },
      };
    }

    const { data: contentType, error: contentTypeError } = await supabase
      .from('content_types')
      .select('name')
      .eq('id', packageData.content_type_id)
      .single();

    if (contentTypeError) {
      console.error('Error fetching content type:', contentTypeError);
      return {
        data: null,
        error: {
          message:
            'Greška pri dohvaćanju tipa sadržaja: ' + contentTypeError.message,
        },
      };
    }

    if (!platform || !contentType) {
      console.error('Platform or content type not found:', {
        platform,
        contentType,
      });
      return {
        data: null,
        error: { message: 'Platform ili tip sadržaja nije pronađen' },
      };
    }

    // Validate and clean video_duration
    let cleanVideoDuration = packageData.video_duration;

    // If content type is not video, set video_duration to null
    if (!contentType.name.toLowerCase().includes('video')) {
      cleanVideoDuration = null;
    } else if (cleanVideoDuration === '' || cleanVideoDuration === undefined) {
      // If it's a video content type but no duration specified, set to null
      cleanVideoDuration = null;
    }

    console.log(
      'Content type:',
      contentType.name,
      'Clean video duration:',
      cleanVideoDuration
    );

    // Generate package name
    const packageName = generatePackageName(
      packageData.quantity,
      platform.name,
      contentType.name,
      cleanVideoDuration
    );

    console.log('Generated package name:', packageName);

    // Create the package
    const insertData = {
      influencer_id: influencerId,
      platform_id: packageData.platform_id,
      content_type_id: packageData.content_type_id,
      quantity: packageData.quantity,
      video_duration: cleanVideoDuration,
      price: packageData.price,
      currency: DEFAULT_CURRENCY,
      auto_generated_name: packageName,
      is_available: true,
    };

    console.log('Inserting package data:', insertData);

    const { data, error } = await supabase
      .from('influencer_platform_pricing')
      .insert(insertData)
      .select()
      .single();

    if (error) {
      console.error('Supabase insert error:', error);
      return {
        data: null,
        error: { message: 'Greška pri kreiranju paketa: ' + error.message },
      };
    }

    console.log('Package created successfully:', data);
    return { data, error: null };
  } catch (err) {
    console.error('Unexpected error in createPricingPackage:', err);
    return {
      data: null,
      error: {
        message:
          'Neočekivana greška: ' +
          (err instanceof Error ? err.message : String(err)),
      },
    };
  }
}

/**
 * Update pricing package
 */
export async function updatePricingPackage(
  packageId: number,
  updates: Partial<PackageCreationData>
) {
  try {
    // If we're updating quantity, platform, content type, or duration, regenerate name
    if (
      updates.quantity ||
      updates.platform_id ||
      updates.content_type_id ||
      updates.video_duration !== undefined
    ) {
      // Get current package data
      const { data: currentPackage } = await supabase
        .from('influencer_platform_pricing')
        .select(
          `
          *,
          platforms!inner(name),
          content_types!inner(name)
        `
        )
        .eq('id', packageId)
        .single();

      if (currentPackage) {
        const newQuantity = updates.quantity || currentPackage.quantity || 1;
        // const newPlatformId = updates.platform_id || currentPackage.platform_id;
        // const newContentTypeId =
        //   updates.content_type_id || currentPackage.content_type_id;
        const newVideoDuration =
          updates.video_duration !== undefined
            ? updates.video_duration
            : currentPackage.video_duration;

        // Get platform and content type names if they changed
        let platformName = currentPackage.platforms.name;
        let contentTypeName = currentPackage.content_types.name;

        if (
          updates.platform_id &&
          updates.platform_id !== currentPackage.platform_id
        ) {
          const { data: platform } = await supabase
            .from('platforms')
            .select('name')
            .eq('id', updates.platform_id)
            .single();
          if (platform) platformName = platform.name;
        }

        if (
          updates.content_type_id &&
          updates.content_type_id !== currentPackage.content_type_id
        ) {
          const { data: contentType } = await supabase
            .from('content_types')
            .select('name')
            .eq('id', updates.content_type_id)
            .single();
          if (contentType) contentTypeName = contentType.name;
        }

        // Generate new package name
        const newPackageName = generatePackageName(
          newQuantity,
          platformName,
          contentTypeName,
          newVideoDuration
        );

        updates = { ...updates, auto_generated_name: newPackageName };
      }
    }

    const { data, error } = await supabase
      .from('influencer_platform_pricing')
      .update(updates)
      .eq('id', packageId)
      .select()
      .single();

    return { data, error };
  } catch (err) {
    return { data: null, error: err };
  }
}

/**
 * Delete pricing package
 */
export async function deletePricingPackage(packageId: number) {
  const { data, error } = await supabase
    .from('influencer_platform_pricing')
    .delete()
    .eq('id', packageId);

  return { data, error };
}

/**
 * Toggle package availability
 */
export async function togglePackageAvailability(
  packageId: number,
  isAvailable: boolean
) {
  const { data, error } = await supabase
    .from('influencer_platform_pricing')
    .update({ is_available: isAvailable })
    .eq('id', packageId)
    .select()
    .single();

  return { data, error };
}

/**
 * Get pricing packages for marketplace display
 */
export async function getInfluencerPricingForMarketplace(influencerId: string) {
  const { data, error } = await supabase
    .from('influencer_platform_pricing')
    .select(
      `
      *,
      platforms!inner(name, icon, slug),
      content_types!inner(name, slug)
    `
    )
    .eq('influencer_id', influencerId)
    .eq('is_available', true)
    .order('price');

  if (error) return { data: null, error };

  // Transform for marketplace display
  const pricing = data.map((item: PricingPackageQueryResult) => ({
    platform_id: item.platform_id,
    platform_name: item.platforms.name,
    platform_icon: item.platforms.icon,
    content_type_id: item.content_type_id,
    content_type_name: item.content_types.name,
    package_name: item.auto_generated_name,
    quantity: item.quantity || 1,
    video_duration: item.video_duration,
    price: item.price,
    currency: item.currency || 'KM',
  }));

  return { data: pricing, error: null };
}
