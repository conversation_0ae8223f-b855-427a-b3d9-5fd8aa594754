'use client';

import { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Send,
  ArrowLeft,
  CheckCircle2,
  User,
  MessageCircle,
} from 'lucide-react';
import { ChatMessage, ChatRoom as ChatRoomType } from '@/lib/chat';
import {
  getChatMessages,
  sendChatMessage,
  markMessagesAsRead,
} from '@/lib/chat';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/contexts/AuthContext';
import { ChatContextBar } from './ChatContextBar';
import {
  formatDate as formatDateUtil,
  formatTime as formatTimeUtil,
} from '@/lib/date-utils';
import { getDisplayName, getInitials } from '@/lib/utils';

interface ChatRoomProps {
  room: ChatRoomType;
  onBack: () => void;
  hideBackButton?: boolean;
}

export function ChatRoom({
  room,
  onBack,
  hideBackButton = false,
}: ChatRoomProps) {
  const { user } = useAuth();
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    // Scroll to bottom when messages change, with a small delay to ensure DOM is updated
    const timer = setTimeout(() => {
      scrollToBottom();
    }, 100);

    return () => clearTimeout(timer);
  }, [messages]);

  useEffect(() => {
    loadMessages();

    // Subscribe to new messages
    const channel = supabase
      .channel(`chat_room_${room.id}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'chat_messages',
          filter: `room_id=eq.${room.id}`,
        },
        async payload => {
          const newMessage = payload.new as ChatMessage;

          // Fetch sender profile data since real-time doesn't include joined data
          try {
            const { data: messageWithProfile, error } = await supabase
              .from('chat_messages')
              .select(
                `
                *,
                sender_profile:profiles!chat_messages_sender_id_fkey(
                  full_name,
                  public_display_name,
                  username,
                  avatar_url
                )
              `
              )
              .eq('id', newMessage.id)
              .single();

            if (error) {
              console.error('Error fetching message with profile:', error);
              return;
            }

            // Only add message if it's not already in the list (avoid duplicates from optimistic updates)
            setMessages(prev => {
              const exists = prev.some(msg => msg.id === newMessage.id);
              if (exists) return prev;
              return [...prev, messageWithProfile];
            });
          } catch (error) {
            console.error('Error processing real-time message:', error);
          }
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [room.id]);

  const loadMessages = async () => {
    setLoading(true);
    try {
      const { data, error } = await getChatMessages(room.id);
      if (error) {
        console.error('Error loading messages:', error);
      } else {
        setMessages(data || []);
        // Mark unread messages as read (messages not sent by current user)
        if (user && data) {
          const unreadMessageIds = data
            .filter(msg => msg.sender_id !== user.id && !msg.read_at)
            .map(msg => msg.id);

          if (unreadMessageIds.length > 0) {
            await markMessagesAsRead(room.id, unreadMessageIds);
          }
        }
      }
    } catch (error) {
      console.error('Error loading messages:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newMessage.trim() || !user || sending) return;

    const messageText = newMessage.trim();
    const userType = user.user_metadata?.user_type || 'influencer';

    // Optimistic update - add message immediately to UI
    const optimisticMessage: ChatMessage = {
      id: `temp-${Date.now()}`, // Temporary ID
      room_id: room.id,
      sender_id: user.id,
      sender_type: userType,
      message_text: messageText,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      is_read: false,
      file_url: null,
      file_name: null,
      file_type: null,
      file_size: null,
      sender_profile: {
        full_name: user.user_metadata?.full_name || user.email || 'You',
        username: user.user_metadata?.username || user.email || 'you',
        avatar_url: user.user_metadata?.avatar_url || null,
      },
    };

    // Add optimistic message to UI
    setMessages(prev => [...prev, optimisticMessage]);
    setNewMessage('');
    setSending(true);

    try {
      const { data, error } = await sendChatMessage(room.id, messageText);

      if (error) {
        console.error('Error sending message:', error);
        // Remove optimistic message on error
        setMessages(prev =>
          prev.filter(msg => msg.id !== optimisticMessage.id)
        );
        setNewMessage(messageText); // Restore message text
      } else if (data) {
        // Replace optimistic message with real message
        setMessages(prev =>
          prev.map(msg => (msg.id === optimisticMessage.id ? data : msg))
        );
      }
    } catch (error) {
      console.error('Error sending message:', error);
      // Remove optimistic message on error
      setMessages(prev => prev.filter(msg => msg.id !== optimisticMessage.id));
      setNewMessage(messageText); // Restore message text
    } finally {
      setSending(false);
    }
  };

  const formatTime = (timestamp: string) => {
    return formatTimeUtil(timestamp);
  };

  const formatDate = (timestamp: string) => {
    return formatDateUtil(timestamp);
  };

  const isMyMessage = (message: ChatMessage) => {
    return user && message.sender_id === user.id;
  };

  const getOtherParticipant = () => {
    if (!user) return null;
    const userType = user.user_metadata?.user_type || 'influencer';

    if (userType === 'business') {
      return room.influencer_profile;
    } else {
      return room.business_profile;
    }
  };

  const otherParticipant = getOtherParticipant();

  return (
    <div className="h-full flex flex-col">
      {/* Fixed Header with Back Button */}
      <div className="flex-shrink-0 bg-white border-b shadow-sm">
        <div className="flex items-center gap-2 px-3 py-2">
          {!hideBackButton && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onBack}
              className="p-1.5 hover:bg-gray-100 rounded-full transition-all duration-200 hover:scale-105"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
          )}

          <div className="relative">
            <Avatar className="h-8 w-8 ring-2 ring-white shadow-md">
              <AvatarImage src={otherParticipant?.avatar_url || ''} />
              <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-500 text-white text-xs">
                {otherParticipant
                  ? getInitials(
                      getDisplayName(otherParticipant) !==
                        'Ime i prezime skriveno'
                        ? getDisplayName(otherParticipant)
                        : otherParticipant.username
                    )
                  : '?'}
              </AvatarFallback>
            </Avatar>
            <div className="absolute -bottom-0.5 -right-0.5 w-2.5 h-2.5 bg-green-500 rounded-full border border-white"></div>
          </div>

          <div className="flex-1 min-w-0">
            <h2 className="font-semibold text-gray-900 text-sm truncate">
              {getDisplayName(otherParticipant) !== 'Ime i prezime skriveno'
                ? getDisplayName(otherParticipant)
                : otherParticipant?.username || 'Nepoznato'}
            </h2>
            <div className="flex items-center gap-2">
              <p className="text-xs text-muted-foreground truncate">
                @{otherParticipant?.username || 'nepoznato'}
              </p>
              <div className="flex items-center gap-1 text-xs text-green-600">
                <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                <span>Online</span>
              </div>
            </div>
          </div>

          <Badge
            variant="secondary"
            className={`text-xs flex-shrink-0 ${
              room.room_type === 'campaign_application'
                ? 'bg-green-100 text-green-800 border-green-200'
                : 'bg-blue-100 text-blue-800 border-blue-200'
            }`}
          >
            <MessageCircle className="h-3 w-3 mr-1" />
            {room.room_type === 'campaign_application'
              ? 'Kampanja'
              : 'Direktna ponuda'}
          </Badge>
        </div>
      </div>

      {/* Context Bar */}
      <div className="flex-shrink-0">
        <ChatContextBar
          campaignApplicationId={room.campaign_application_id}
          offerId={room.offer_id}
        />
      </div>

      {/* Chat Content */}
      <div className="flex-1 overflow-y-auto bg-gradient-to-b from-gray-50/30 to-white p-4 space-y-4">
        {loading ? (
          <div className="space-y-4">
            {[1, 2, 3, 4].map(i => (
              <div
                key={i}
                className={`flex ${i % 2 === 0 ? 'justify-end' : 'justify-start'}`}
              >
                <div className="max-w-[70%] space-y-2">
                  <Skeleton className="h-12 w-48 rounded-lg" />
                  <Skeleton className="h-3 w-16" />
                </div>
              </div>
            ))}
          </div>
        ) : messages.length === 0 ? (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center text-muted-foreground py-12">
              <div className="w-16 h-16 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <MessageCircle className="h-8 w-8 text-blue-500" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">
                Počnite razgovor
              </h3>
              <p className="text-sm">
                Pošaljite prvu poruku da započnete komunikaciju!
              </p>
            </div>
          </div>
        ) : (
          messages.map((message, index) => {
            const showDate =
              index === 0 ||
              formatDate(message.created_at || '') !==
                formatDate(messages[index - 1]?.created_at || '');

            return (
              <div key={message.id}>
                {showDate && (
                  <div className="text-center text-xs text-muted-foreground mb-4">
                    <div className="bg-white/80 backdrop-blur-sm px-3 py-1 rounded-full inline-block border">
                      {formatDate(message.created_at || '')}
                    </div>
                  </div>
                )}

                <div
                  className={`flex ${isMyMessage(message) ? 'justify-end' : 'justify-start'}`}
                >
                  <div
                    className={`max-w-[70%] ${isMyMessage(message) ? 'order-2' : 'order-1'}`}
                  >
                    <div
                      className={`rounded-2xl px-4 py-3 shadow-sm ${
                        isMyMessage(message)
                          ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white'
                          : 'bg-white border border-gray-200'
                      }`}
                    >
                      <p className="text-sm leading-relaxed">
                        {message.message_text}
                      </p>
                    </div>
                    <div
                      className={`flex items-center gap-1 mt-1 ${
                        isMyMessage(message) ? 'justify-end' : 'justify-start'
                      }`}
                    >
                      <p className="text-xs text-muted-foreground">
                        {formatTime(message.created_at || '')}
                      </p>
                      {isMyMessage(message) && (
                        <div className="flex items-center gap-1">
                          {!message.id.startsWith('temp-') && (
                            <CheckCircle2 className="h-3 w-3 text-blue-500" />
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            );
          })
        )}

        {/* Typing Indicator */}
        {isTyping && (
          <div className="flex justify-start">
            <div className="max-w-[70%]">
              <div className="bg-gray-100 rounded-2xl px-4 py-3 shadow-sm">
                <div className="flex items-center gap-1">
                  <div className="flex gap-1">
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                    <div
                      className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                      style={{ animationDelay: '0.1s' }}
                    ></div>
                    <div
                      className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                      style={{ animationDelay: '0.2s' }}
                    ></div>
                  </div>
                  <span className="text-xs text-gray-500 ml-2">kuca...</span>
                </div>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Fixed Message Input */}
      <div className="flex-shrink-0 border-t bg-white px-3 py-2">
        <form onSubmit={handleSendMessage} className="flex items-center gap-2">
          <Input
            value={newMessage}
            onChange={e => setNewMessage(e.target.value)}
            placeholder="Napišite poruku..."
            disabled={sending}
            className="flex-1 rounded-full border-gray-300 focus:border-blue-500 focus:ring-blue-500 h-10"
          />
          <Button
            type="submit"
            disabled={!newMessage.trim() || sending}
            className="rounded-full w-10 h-10 p-0 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 shadow-lg flex-shrink-0"
          >
            {sending ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            ) : (
              <Send className="h-4 w-4" />
            )}
          </Button>
        </form>
      </div>
    </div>
  );
}
