'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { toast } from 'sonner';
import { ArrowLeft, Mail, Send, Loader2 } from 'lucide-react';
import Link from 'next/link';

export default function EmailDemoPage() {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [emailType, setEmailType] = useState<string>('');

  const sendTestEmail = async (type: string) => {
    if (!email) {
      toast.error('Molimo unesite email adresu');
      return;
    }

    setIsLoading(true);

    try {
      let endpoint = '';
      let payload = {};

      switch (type) {
        case 'verification':
          endpoint = '/api/emails/verification';
          payload = {
            email,
            verificationUrl: `${window.location.origin}/auth/verify?token=demo_token_123&email=${encodeURIComponent(email)}`,
          };
          break;

        case 'password-reset':
          endpoint = '/api/emails/password-reset';
          payload = {
            email,
            resetUrl: `${window.location.origin}/auth/reset-password?token=demo_reset_token_456&email=${encodeURIComponent(email)}`,
          };
          break;

        case 'welcome-influencer':
          endpoint = '/api/emails/welcome';
          payload = {
            email,
            userName: 'Demo Influencer',
            userType: 'influencer',
          };
          break;

        case 'welcome-business':
          endpoint = '/api/emails/welcome';
          payload = {
            email,
            userName: 'Demo Business',
            userType: 'business',
          };
          break;

        case 'notification':
          endpoint = '/api/emails/notification';
          payload = {
            email,
            userName: 'Demo User',
            notificationType: 'new_offer',
            details: {
              'Naziv kampanje': 'Demo Kampanja za Testiranje',
              Brend: 'Demo Brend',
              Budžet: '500 EUR',
              Deadline: '15.09.2024',
            },
          };
          break;

        default:
          toast.error('Nepoznat tip email-a');
          return;
      }

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      const result = await response.json();

      if (response.ok) {
        toast.success('Email uspešno poslat!');
      } else {
        toast.error(result.error || 'Greška pri slanju email-a');
      }
    } catch (error) {
      console.error('Email error:', error);
      toast.error('Došlo je do greške pri slanju email-a');
    } finally {
      setIsLoading(false);
    }
  };

  const emailTypes = [
    {
      value: 'verification',
      label: 'Email Verifikacija',
      description: 'Test email za potvrdu email adrese',
    },
    {
      value: 'password-reset',
      label: 'Resetovanje Lozinke',
      description: 'Test email za resetovanje lozinke',
    },
    {
      value: 'welcome-influencer',
      label: 'Dobrodošlica - Influencer',
      description: 'Dobrodošlica za influensere',
    },
    {
      value: 'welcome-business',
      label: 'Dobrodošlica - Business',
      description: 'Dobrodošlica za brendove',
    },
    {
      value: 'notification',
      label: 'Notifikacija',
      description: 'Test notifikacioni email',
    },
  ];

  return (
    <div className="min-h-screen bg-instagram-story relative overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-black/5"></div>
      <div className="absolute top-10 right-10 w-64 h-64 bg-white/5 rounded-full blur-3xl"></div>
      <div className="absolute bottom-10 left-10 w-80 h-80 bg-white/5 rounded-full blur-3xl"></div>

      <header className="relative z-10 border-b border-white/20 backdrop-blur-sm bg-white/10">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <Link
            href="/dashboard"
            className="flex items-center space-x-2 text-white/80 hover:text-white transition-colors"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Nazad na Dashboard</span>
          </Link>
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center border border-white/30">
              <Mail className="h-4 w-4 text-white" />
            </div>
            <span className="text-xl font-bold text-white">Email Demo</span>
          </div>
        </div>
      </header>

      <main className="relative z-10 container mx-auto px-4 py-12">
        <div className="max-w-2xl mx-auto space-y-6">
          <Card className="glass-instagram border-white/20 shadow-2xl">
            <CardHeader>
              <CardTitle className="text-white text-center">
                INFLUEXUS Email Templates Demo
              </CardTitle>
              <CardDescription className="text-white/70 text-center">
                Testirajte designirane email template-ove koji koriste vaš
                gradient dizajn
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="email" className="text-white">
                  Email Adresa za Testiranje
                </Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={e => setEmail(e.target.value)}
                  className="bg-white/10 border-white/30 text-white placeholder:text-white/50 focus:border-white/50 focus:ring-white/20"
                />
              </div>

              <div className="space-y-4">
                <h3 className="text-white font-semibold">
                  Dostupni Email Template-ovi:
                </h3>
                <div className="grid gap-3">
                  {emailTypes.map(type => (
                    <Card
                      key={type.value}
                      className="bg-white/5 border-white/20"
                    >
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <h4 className="text-white font-medium">
                              {type.label}
                            </h4>
                            <p className="text-white/70 text-sm">
                              {type.description}
                            </p>
                          </div>
                          <Button
                            onClick={() => sendTestEmail(type.value)}
                            disabled={isLoading || !email}
                            className="bg-white text-instagram-purple hover:bg-white/90 font-semibold"
                          >
                            {isLoading ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                              <>
                                <Send className="h-4 w-4 mr-2" />
                                Pošalji
                              </>
                            )}
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>

              <div className="bg-white/10 border border-white/20 rounded-lg p-4">
                <h4 className="text-white font-medium mb-2">
                  📧 Detalji o Email Servisu
                </h4>
                <ul className="text-white/80 text-sm space-y-1">
                  <li>
                    • Email servis: <strong>Brevo</strong>
                  </li>
                  <li>
                    • Sender: <strong><EMAIL></strong>
                  </li>
                  <li>
                    • Template dizajn:{' '}
                    <strong>Instagram gradijenti iz vašeg sistema</strong>
                  </li>
                  <li>• Responsive dizajn za sve uređaje</li>
                  <li>• Podržava HTML i text verzije</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
}
