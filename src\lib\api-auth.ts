import { NextRequest } from 'next/server';
import { supabase } from './supabase';

export interface AuthenticatedRequest extends NextRequest {
  user?: {
    id: string;
    email: string;
    user_type: 'influencer' | 'business';
  };
}

export interface AuthValidationOptions {
  requireAuth?: boolean;
  allowedUserTypes?: ('influencer' | 'business')[];
  requireBearerToken?: boolean;
  webhookSecret?: string;
}

/**
 * Comprehensive JWT token validation for API routes
 */
export async function validateJWTToken(
  request: NextRequest,
  options: AuthValidationOptions = {}
): Promise<{
  success: boolean;
  user?: Record<string, unknown>;
  error?: string;
  statusCode?: number;
}> {
  const {
    requireAuth = true,
    allowedUserTypes = ['influencer', 'business'],
    requireBearerToken = true,
    webhookSecret,
  } = options;

  try {
    // Check for webhook authentication if specified
    if (webhookSecret) {
      const authHeader = request.headers.get('authorization');
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return {
          success: false,
          error: 'Missing or invalid authorization header',
          statusCode: 401,
        };
      }

      const token = authHeader.substring(7); // Remove 'Bearer ' prefix
      if (token !== webhookSecret) {
        return {
          success: false,
          error: 'Invalid webhook token',
          statusCode: 401,
        };
      }

      return { success: true };
    }

    // Skip auth validation if not required
    if (!requireAuth) {
      return { success: true };
    }

    // Get authorization header
    const authHeader = request.headers.get('authorization');

    if (
      requireBearerToken &&
      (!authHeader || !authHeader.startsWith('Bearer '))
    ) {
      return {
        success: false,
        error:
          'Missing or invalid authorization header. Expected format: Bearer <token>',
        statusCode: 401,
      };
    }

    let accessToken: string;

    if (authHeader?.startsWith('Bearer ')) {
      accessToken = authHeader.substring(7); // Remove 'Bearer ' prefix
    } else {
      // Try to get token from cookies as fallback
      const cookies = request.cookies;
      const tokenFromCookie = cookies.get('supabase-auth-token')?.value;

      if (!tokenFromCookie) {
        return {
          success: false,
          error: 'No authentication token provided',
          statusCode: 401,
        };
      }

      accessToken = tokenFromCookie;
    }

    // Validate token with Supabase
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser(accessToken);

    if (authError || !user) {
      return {
        success: false,
        error: 'Invalid or expired token',
        statusCode: 401,
      };
    }

    // Get user profile for additional validation
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('user_type, profile_completed')
      .eq('id', user.id)
      .single();

    if (profileError || !profile) {
      return {
        success: false,
        error: 'User profile not found',
        statusCode: 403,
      };
    }

    // Check if user type is allowed
    if (
      allowedUserTypes.length > 0 &&
      !allowedUserTypes.includes(profile.user_type)
    ) {
      return {
        success: false,
        error: `Access forbidden. Required user type: ${allowedUserTypes.join(' or ')}`,
        statusCode: 403,
      };
    }

    // Return successful validation with user data
    return {
      success: true,
      user: {
        id: user.id,
        email: user.email!,
        user_type: profile.user_type,
        profile_completed: profile.profile_completed,
        metadata: user.user_metadata,
      },
    };
  } catch (error) {
    console.error('JWT validation error:', error);
    return {
      success: false,
      error: 'Authentication service error',
      statusCode: 500,
    };
  }
}

/**
 * Rate limiting implementation using in-memory store
 * In production, consider using Redis or a proper rate limiting service
 */
class RateLimiter {
  private requests: Map<string, { count: number; resetTime: number }> =
    new Map();
  private readonly maxRequests: number;
  private readonly windowMs: number;

  constructor(maxRequests = 100, windowMs = 60 * 1000) {
    // 100 requests per minute
    this.maxRequests = maxRequests;
    this.windowMs = windowMs;
  }

  isAllowed(identifier: string): {
    allowed: boolean;
    remaining: number;
    resetTime: number;
  } {
    const now = Date.now();
    const record = this.requests.get(identifier);

    if (!record || now > record.resetTime) {
      // First request or window expired
      const resetTime = now + this.windowMs;
      this.requests.set(identifier, { count: 1, resetTime });
      return { allowed: true, remaining: this.maxRequests - 1, resetTime };
    }

    if (record.count >= this.maxRequests) {
      // Rate limit exceeded
      return { allowed: false, remaining: 0, resetTime: record.resetTime };
    }

    // Increment count
    record.count++;
    this.requests.set(identifier, record);

    return {
      allowed: true,
      remaining: this.maxRequests - record.count,
      resetTime: record.resetTime,
    };
  }

  // Clean up expired entries periodically
  cleanup() {
    const now = Date.now();
    for (const [key, record] of this.requests.entries()) {
      if (now > record.resetTime) {
        this.requests.delete(key);
      }
    }
  }
}

// Global rate limiter instances
const generalLimiter = new RateLimiter(100, 60 * 1000); // 100 req/min
const strictLimiter = new RateLimiter(10, 60 * 1000); // 10 req/min for sensitive operations

export function applyRateLimit(
  request: NextRequest,
  options: {
    strict?: boolean;
    customLimit?: { maxRequests: number; windowMs: number };
  } = {}
): { allowed: boolean; remaining: number; resetTime: number } {
  const clientIP =
    request.headers.get('x-forwarded-for') ||
    request.headers.get('x-real-ip') ||
    'unknown';

  const identifier = `${clientIP}:${request.nextUrl.pathname}`;

  if (options.customLimit) {
    const customLimiter = new RateLimiter(
      options.customLimit.maxRequests,
      options.customLimit.windowMs
    );
    return customLimiter.isAllowed(identifier);
  }

  const limiter = options.strict ? strictLimiter : generalLimiter;
  return limiter.isAllowed(identifier);
}

/**
 * CORS configuration
 */
export function getCORSHeaders(origin?: string) {
  const allowedOrigins = [
    'http://localhost:3000',
    'http://localhost:3001',
    'https://your-domain.com', // Replace with your production domain
    process.env.NEXT_PUBLIC_APP_URL,
  ].filter(Boolean);

  const isOriginAllowed = !origin || allowedOrigins.includes(origin);

  return {
    'Access-Control-Allow-Origin': isOriginAllowed ? origin || '*' : 'null',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers':
      'Content-Type, Authorization, X-Requested-With',
    'Access-Control-Allow-Credentials': 'true',
    'Access-Control-Max-Age': '86400', // 24 hours
  };
}

/**
 * Request body validation schemas
 */
export const validationSchemas = {
  // Common schemas that can be reused across different endpoints
  email: {
    type: 'string',
    format: 'email',
    minLength: 5,
    maxLength: 254,
  },

  password: {
    type: 'string',
    minLength: 8,
    maxLength: 128,
  },

  uuid: {
    type: 'string',
    pattern:
      '^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$',
  },
};

/**
 * Validate request body against a schema
 */
export function validateRequestBody(
  body: Record<string, unknown>,
  schema: Record<string, unknown>
): { valid: boolean; errors?: string[] } {
  const errors: string[] = [];

  // Simple validation implementation
  // In production, consider using a library like Joi or Yup
  if (schema.required) {
    for (const field of schema.required) {
      if (
        !(field in body) ||
        body[field] === null ||
        body[field] === undefined
      ) {
        errors.push(`Field '${field}' is required`);
      }
    }
  }

  if (schema.properties) {
    for (const [field, rules] of Object.entries(schema.properties) as [
      string,
      any,
    ][]) {
      const value = body[field];

      if (value !== undefined && value !== null) {
        // Type validation
        if (rules.type && typeof value !== rules.type) {
          errors.push(`Field '${field}' must be of type ${rules.type}`);
        }

        // String validations
        if (rules.type === 'string' && typeof value === 'string') {
          if (rules.minLength && value.length < rules.minLength) {
            errors.push(
              `Field '${field}' must be at least ${rules.minLength} characters long`
            );
          }

          if (rules.maxLength && value.length > rules.maxLength) {
            errors.push(
              `Field '${field}' must be at most ${rules.maxLength} characters long`
            );
          }

          if (rules.pattern && !new RegExp(rules.pattern).test(value)) {
            errors.push(`Field '${field}' does not match required pattern`);
          }

          if (
            rules.format === 'email' &&
            !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)
          ) {
            errors.push(`Field '${field}' must be a valid email address`);
          }
        }

        // Number validations
        if (rules.type === 'number' && typeof value === 'number') {
          if (rules.min !== undefined && value < rules.min) {
            errors.push(`Field '${field}' must be at least ${rules.min}`);
          }

          if (rules.max !== undefined && value > rules.max) {
            errors.push(`Field '${field}' must be at most ${rules.max}`);
          }
        }
      }
    }
  }

  return {
    valid: errors.length === 0,
    errors: errors.length > 0 ? errors : undefined,
  };
}

/**
 * Cleanup function to be called periodically to remove expired rate limit entries
 */
export function cleanupRateLimiters() {
  generalLimiter.cleanup();
  strictLimiter.cleanup();
}

// Set up periodic cleanup (every 5 minutes)
if (typeof window === 'undefined') {
  setInterval(cleanupRateLimiters, 5 * 60 * 1000);
}
