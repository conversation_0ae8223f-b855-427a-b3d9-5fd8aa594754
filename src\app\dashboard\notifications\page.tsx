'use client';

import { useState, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { TabsWithBadge } from '@/components/ui/tabs-with-badge';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import {
  Bell,
  Check,
  CheckCheck,
  Inbox,
  MessageCircle,
  DollarSign,
  FileText,
  Building2,
  User,
  Loader2,
} from 'lucide-react';
import {
  getUserNotifications,
  markNotificationAsRead,
  markAllNotificationsAsRead,
  getUnreadNotificationCount,
  type Notification,
} from '@/lib/notifications';
import { useAuth } from '@/contexts/AuthContext';
import { formatDistanceToNow } from 'date-fns';
import { hr } from 'date-fns/locale';
import { toast } from 'sonner';
import Link from 'next/link';
import { useInfiniteScroll } from '@/../hooks/useInfiniteScroll';
import { InfiniteScroll } from '@/components/ui/infinite-scroll';

const NOTIFICATIONS_PER_PAGE = 15;

export default function NotificationsPage() {
  const { user } = useAuth();
  const [unreadCount, setUnreadCount] = useState(0);
  const [totalCount, setTotalCount] = useState(0);
  const [activeTab, setActiveTab] = useState<'all' | 'unread' | 'read'>(
    'unread'
  );

  // Fetch function za useInfiniteScroll - koristi database filtriranje
  const fetchNotifications = useCallback(
    async (offset: number, limit: number) => {
      if (!user) {
        return { data: [], hasMore: false, error: 'User not authenticated' };
      }

      try {
        // Odredi readStatus na osnovu activeTab
        let readStatus: boolean | null = null;
        switch (activeTab) {
          case 'unread':
            readStatus = false;
            break;
          case 'read':
            readStatus = true;
            break;
          case 'all':
          default:
            readStatus = null; // ne filtrira
            break;
        }

        // Pozovi getUserNotifications sa readStatus parametrom - database će filtrirati
        const { data, error } = await getUserNotifications(
          user.id,
          limit + 1,
          offset,
          readStatus
        );

        if (error) {
          return { data: [], hasMore: false, error };
        }

        const notifications = data || [];

        // Standard infinite scroll logika
        const hasMore = notifications.length > limit;
        const finalData = hasMore ? notifications.slice(0, -1) : notifications;

        return {
          data: finalData,
          hasMore,
          error: null,
        };
      } catch (error) {
        return { data: [], hasMore: false, error };
      }
    },
    [user, activeTab]
  );

  // UseInfiniteScroll hook
  const {
    data: notifications,
    isLoading,
    isLoadingMore,
    hasMore,
    error,
    actions: { loadMore, refresh, reset },
  } = useInfiniteScroll<Notification>({
    fetchData: fetchNotifications,
    limit: NOTIFICATIONS_PER_PAGE,
    cacheKey: `notifications-${user?.id}-${activeTab}`,
    dependencies: [user?.id, activeTab],
  });

  const loadCounts = useCallback(async () => {
    if (!user) return;

    try {
      // Load unread count
      const { count: unreadCount, error: unreadError } =
        await getUnreadNotificationCount(user.id);
      if (unreadError) {
        console.error('Error loading unread count:', unreadError);
      } else {
        setUnreadCount(unreadCount);
      }

      // Load total count - get first page to count all notifications
      const { data: allNotifications, error: totalError } =
        await getUserNotifications(user.id, 1000, 0);
      if (totalError) {
        console.error('Error loading total count:', totalError);
      } else {
        setTotalCount(allNotifications?.length || 0);
      }
    } catch (error) {
      console.error('Error loading counts:', error);
    }
  }, [user]);

  useEffect(() => {
    if (user) {
      loadCounts();
    }
  }, [user, loadCounts]);

  const handleMarkAsRead = async (notificationId: string) => {
    try {
      const { error } = await markNotificationAsRead(notificationId);
      if (error) {
        toast.error('Greška pri označavanju notifikacije');
      } else {
        // Refresh data to get updated read status
        refresh();
        loadCounts();
      }
    } catch (error) {
      console.error('Error marking notification as read:', error);
      toast.error('Greška pri označavanju notifikacije');
    }
  };

  const handleMarkAllAsRead = async () => {
    if (!user) return;

    try {
      const { error } = await markAllNotificationsAsRead(user.id);
      if (error) {
        toast.error('Greška pri označavanju notifikacija');
      } else {
        // Refresh data to get updated read status
        refresh();
        loadCounts();
        toast.success('Sve notifikacije su označene kao pročitane');
      }
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      toast.error('Greška pri označavanju notifikacija');
    }
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'offer_received':
      case 'offer_accepted':
      case 'offer_rejected':
      case 'order_received':
        return <Inbox className="h-5 w-5" />;
      case 'campaign_application':
      case 'campaign_accepted':
      case 'campaign_rejected':
      case 'application_accepted_payment_pending':
        return <FileText className="h-5 w-5" />;
      case 'job_completion_submitted':
      case 'job_completion_approved':
      case 'job_completion_rejected':
        return <CheckCheck className="h-5 w-5" />;
      case 'message_received':
        return <MessageCircle className="h-5 w-5" />;
      case 'payment_received':
      case 'payment_required_offer_accepted':
      case 'payment_required_order_accepted':
      case 'payment_completed_work_ready':
        return <DollarSign className="h-5 w-5" />;
      default:
        return <Bell className="h-5 w-5" />;
    }
  };

  const getNotificationLink = (notification: Notification) => {
    switch (notification.type) {
      case 'offer_received':
      case 'offer_accepted':
      case 'offer_rejected':
      case 'payment_completed_work_ready':
        if (notification.data.offer_id || notification.data.project_id) {
          const id = notification.data.offer_id || notification.data.project_id;
          return `/dashboard/influencer/offers/${id}`;
        }
        return '/dashboard/influencer/offers';
      case 'order_received':
        if (notification.data.order_id) {
          return `/dashboard/influencer/offers/${notification.data.order_id}`;
        }
        return '/dashboard/influencer/offers';
      case 'payment_required_offer_accepted':
      case 'payment_required_order_accepted':
        if (notification.data.offer_id) {
          return `/dashboard/biznis/offers/${notification.data.offer_id}`;
        }
        if (notification.data.order_id) {
          return `/dashboard/biznis/offers/${notification.data.order_id}`;
        }
        return '/dashboard/biznis/offers';
      case 'job_completion_submitted':
        if (notification.data.direct_offer_id) {
          return `/dashboard/biznis/offers/${notification.data.direct_offer_id}`;
        }
        return '/dashboard/biznis/offers';
      case 'job_completion_approved':
      case 'job_completion_rejected':
        if (notification.data.job_completion_id) {
          return '/dashboard/job-completions';
        }
        return '/dashboard/influencer/offers';
      case 'campaign_application':
        return '/dashboard/campaigns';
      case 'campaign_accepted':
      case 'campaign_rejected':
      case 'application_accepted_payment_pending':
        if (notification.data.application_id) {
          return `/dashboard/influencer/applications/${notification.data.application_id}`;
        }
        return '/dashboard/influencer/campaigns';
      case 'message_received':
        return '/dashboard/messages';
      case 'payment_received':
        return '/dashboard/influencer/earnings';
      default:
        return '/dashboard';
    }
  };

  if (!user) return null;

  const tabs = [
    {
      name: 'Nepročitane',
      value: 'unread',
      icon: <Inbox className="h-4 w-4" />,
      count: unreadCount,
    },
    {
      name: 'Pročitane',
      value: 'read',
      icon: <CheckCheck className="h-4 w-4" />,
      count: Math.max(0, totalCount - unreadCount),
    },
    {
      name: 'Sve',
      value: 'all',
      icon: <Bell className="h-4 w-4" />,
      count: totalCount,
    },
  ];

  return (
    <DashboardLayout>
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
              Notifikacije
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              {unreadCount > 0
                ? `${unreadCount} nepročitanih notifikacija`
                : 'Sve vaše notifikacije su pročitane'}
            </p>
          </div>
          {unreadCount > 0 && (
            <Button
              onClick={handleMarkAllAsRead}
              variant="outline"
              className="gap-2 bg-gradient-to-r from-purple-400 via-pink-400 to-purple-500 hover:from-purple-500 hover:via-pink-500 hover:to-purple-600 text-white border-none"
            >
              <CheckCheck className="h-4 w-4" />
              Označi sve kao pročitane
            </Button>
          )}
        </div>

        <TabsWithBadge
          tabs={tabs}
          value={activeTab}
          onValueChange={setActiveTab}
        >
          {/* Tab content */}
          <div className="mt-6">
            {isLoading ? (
              <div className="flex items-center justify-center py-12">
                <Loader2 className="h-8 w-8 animate-spin text-purple-500" />
              </div>
            ) : notifications.length === 0 ? (
              <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
                <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
                <div className="relative p-12 text-center">
                  <Bell className="h-16 w-16 text-purple-400 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-2">
                    {activeTab === 'unread'
                      ? 'Nema nepročitanih notifikacija'
                      : activeTab === 'read'
                        ? 'Nema pročitanih notifikacija'
                        : 'Nema notifikacija'}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400">
                    {activeTab === 'unread'
                      ? 'Sve vaše notifikacije su pročitane'
                      : 'Ovdje će se pojaviti vaše notifikacije'}
                  </p>
                </div>
              </div>
            ) : (
              <InfiniteScroll
                data={notifications}
                isLoading={isLoading}
                isLoadingMore={isLoadingMore}
                hasMore={hasMore}
                loadMore={loadMore}
                cacheKey={`notifications-${user?.id}-${activeTab}`}
              >
                <div className="space-y-3">
                  {notifications.map(notification => (
                    <div
                      key={notification.id}
                      className={`relative overflow-hidden rounded-2xl border transition-all duration-200 hover:shadow-lg ${
                        notification.read
                          ? 'bg-white/60 dark:bg-gray-800/40 border-gray-200/50 dark:border-gray-800/30'
                          : 'bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border-purple-200/50 dark:border-purple-800/30 border-l-4 border-l-purple-500'
                      }`}
                    >
                      {!notification.read && (
                        <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
                      )}
                      <div className="relative p-6">
                        <Link
                          href={getNotificationLink(notification)}
                          onClick={() => {
                            if (!notification.read) {
                              handleMarkAsRead(notification.id);
                            }
                          }}
                          className="block"
                        >
                          <div className="flex items-start gap-4">
                            <div className="flex-shrink-0 mt-1">
                              <div
                                className={`p-3 rounded-full ${
                                  notification.read
                                    ? 'bg-gray-100 dark:bg-gray-700'
                                    : 'bg-purple-100 dark:bg-purple-900/50'
                                }`}
                              >
                                <div
                                  className={
                                    notification.read
                                      ? 'text-gray-600 dark:text-gray-400'
                                      : 'text-purple-600 dark:text-purple-400'
                                  }
                                >
                                  {getNotificationIcon(notification.type)}
                                </div>
                              </div>
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-start justify-between gap-4">
                                <h4
                                  className={`font-semibold text-base ${
                                    notification.read
                                      ? 'text-gray-600 dark:text-gray-400'
                                      : 'text-gray-900 dark:text-gray-100'
                                  }`}
                                >
                                  {notification.title}
                                </h4>
                                <div className="flex items-center gap-3 flex-shrink-0">
                                  <span className="text-xs text-gray-500 dark:text-gray-400">
                                    {formatDistanceToNow(
                                      new Date(notification.created_at),
                                      { addSuffix: true, locale: hr }
                                    )}
                                  </span>
                                  {!notification.read && (
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={e => {
                                        e.preventDefault();
                                        e.stopPropagation();
                                        handleMarkAsRead(notification.id);
                                      }}
                                      className="h-8 w-8 p-0 text-purple-600 hover:text-purple-700 hover:bg-purple-100 dark:text-purple-400 dark:hover:text-purple-300 dark:hover:bg-purple-900/30"
                                    >
                                      <Check className="h-4 w-4" />
                                    </Button>
                                  )}
                                </div>
                              </div>
                              <p
                                className={`text-sm mt-2 line-clamp-2 ${
                                  notification.read
                                    ? 'text-gray-500 dark:text-gray-400'
                                    : 'text-gray-700 dark:text-gray-300'
                                }`}
                              >
                                {notification.message}
                              </p>
                            </div>
                          </div>
                        </Link>
                      </div>
                    </div>
                  ))}
                </div>
              </InfiniteScroll>
            )}
          </div>
        </TabsWithBadge>
      </div>
    </DashboardLayout>
  );
}
