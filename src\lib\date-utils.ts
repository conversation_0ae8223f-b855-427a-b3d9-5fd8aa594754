/**
 * Date utility functions for consistent date formatting across the application
 */

/**
 * Format a date to DD.MM.YYYY format
 * @param date - Date string, Date object, or null
 * @returns Formatted date string in DD.MM.YYYY format or 'N/A' if invalid
 */
export function formatDate(date: string | Date | null | undefined): string {
  if (!date) return 'N/A';

  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;

    if (isNaN(dateObj.getTime())) {
      return 'N/A';
    }

    const day = dateObj.getDate().toString().padStart(2, '0');
    const month = (dateObj.getMonth() + 1).toString().padStart(2, '0');
    const year = dateObj.getFullYear();

    return `${day}.${month}.${year}`;
  } catch (error) {
    return 'N/A';
  }
}

/**
 * Format a date to DD.MM.YYYY HH:MM format
 * @param date - Date string, Date object, or null
 * @returns Formatted date string in DD.MM.YYYY HH:MM format or 'N/A' if invalid
 */
export function formatDateTime(date: string | Date | null | undefined): string {
  if (!date) return 'N/A';

  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;

    if (isNaN(dateObj.getTime())) {
      return 'N/A';
    }

    const day = dateObj.getDate().toString().padStart(2, '0');
    const month = (dateObj.getMonth() + 1).toString().padStart(2, '0');
    const year = dateObj.getFullYear();
    const hours = dateObj.getHours().toString().padStart(2, '0');
    const minutes = dateObj.getMinutes().toString().padStart(2, '0');

    return `${day}.${month}.${year} ${hours}:${minutes}`;
  } catch (error) {
    return 'N/A';
  }
}

/**
 * Format time only in HH:MM format
 * @param date - Date string, Date object, or null
 * @returns Formatted time string in HH:MM format or 'N/A' if invalid
 */
export function formatTime(date: string | Date | null | undefined): string {
  if (!date) return 'N/A';

  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;

    if (isNaN(dateObj.getTime())) {
      return 'N/A';
    }

    const hours = dateObj.getHours().toString().padStart(2, '0');
    const minutes = dateObj.getMinutes().toString().padStart(2, '0');

    return `${hours}:${minutes}`;
  } catch (error) {
    return 'N/A';
  }
}

/**
 * Check if a date is today
 * @param date - Date string, Date object, or null
 * @returns True if the date is today, false otherwise
 */
export function isToday(date: string | Date | null | undefined): boolean {
  if (!date) return false;

  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const today = new Date();

    return (
      dateObj.getDate() === today.getDate() &&
      dateObj.getMonth() === today.getMonth() &&
      dateObj.getFullYear() === today.getFullYear()
    );
  } catch (error) {
    return false;
  }
}

/**
 * Check if a date is yesterday
 * @param date - Date string, Date object, or null
 * @returns True if the date is yesterday, false otherwise
 */
export function isYesterday(date: string | Date | null | undefined): boolean {
  if (!date) return false;

  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);

    return (
      dateObj.getDate() === yesterday.getDate() &&
      dateObj.getMonth() === yesterday.getMonth() &&
      dateObj.getFullYear() === yesterday.getFullYear()
    );
  } catch (error) {
    return false;
  }
}

/**
 * Format date for chat messages (shows time if today, date if older)
 * @param date - Date string, Date object, or null
 * @returns Formatted string appropriate for chat context
 */
export function formatChatDate(date: string | Date | null | undefined): string {
  if (!date) return 'N/A';

  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;

    if (isToday(dateObj)) {
      return formatTime(dateObj);
    } else if (isYesterday(dateObj)) {
      return 'Jučer';
    } else {
      return formatDate(dateObj);
    }
  } catch (error) {
    return 'N/A';
  }
}

/**
 * Format reset date for limits with relative time display
 * @param resetDate ISO string datuma
 * @returns Formatiran datum
 */
export function formatResetDate(resetDate: string): string {
  const date = new Date(resetDate);
  const now = new Date();

  const diffTime = date.getTime() - now.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays === 1) {
    return 'sutra';
  } else if (diffDays <= 7) {
    return `za ${diffDays} dana`;
  } else {
    return date.toLocaleDateString('sr-Latn-BA', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
    });
  }
}
