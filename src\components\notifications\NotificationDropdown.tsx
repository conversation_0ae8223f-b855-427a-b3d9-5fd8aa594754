'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuItem,
} from '@/components/ui/dropdown-menu';
import {
  Bell,
  Check,
  CheckCheck,
  Inbox,
  MessageCircle,
  DollarSign,
  FileText,
  Building2,
  User,
} from 'lucide-react';
import {
  getUserNotifications,
  markNotificationAsRead,
  markAllNotificationsAsRead,
  getUnreadNotificationCount,
  type Notification,
} from '@/lib/notifications';
import { useAuth } from '@/contexts/AuthContext';
import { formatDistanceToNow } from 'date-fns';
import { hr } from 'date-fns/locale';
import { toast } from 'sonner';
import Link from 'next/link';

export function NotificationDropdown() {
  const { user } = useAuth();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    if (user) {
      loadNotifications();
      loadUnreadCount();
    }
  }, [user]);

  const loadNotifications = async () => {
    if (!user) return;

    setIsLoading(true);
    try {
      const { data, error } = await getUserNotifications(user.id, 8, 0);
      if (error) {
        console.error('Error loading notifications:', error);
      } else {
        // Prikaži maksimalno 8 notifikacija: unread + najnovije read
        const allNotifications = data || [];
        const unreadNotifications = allNotifications.filter(n => !n.read);
        const readNotifications = allNotifications.filter(n => n.read);

        const displayNotifications = [];

        // Dodaj sve unread notifikacije
        displayNotifications.push(...unreadNotifications);

        // Dodaj read notifikacije do ukupno 8
        const remainingSlots = 8 - unreadNotifications.length;
        if (remainingSlots > 0) {
          displayNotifications.push(
            ...readNotifications.slice(0, remainingSlots)
          );
        }

        setNotifications(displayNotifications);
      }
    } catch (error) {
      console.error('Error loading notifications:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadUnreadCount = async () => {
    if (!user) return;

    try {
      const { count, error } = await getUnreadNotificationCount(user.id);
      if (error) {
        console.error('Error loading unread count:', error);
      } else {
        setUnreadCount(count);
      }
    } catch (error) {
      console.error('Error loading unread count:', error);
    }
  };

  const handleMarkAsRead = async (notificationId: string) => {
    try {
      const { error } = await markNotificationAsRead(notificationId);
      if (error) {
        toast.error('Greška pri označavanju notifikacije');
      } else {
        setNotifications(prev =>
          prev.map(n => (n.id === notificationId ? { ...n, read: true } : n))
        );
        setUnreadCount(prev => Math.max(0, prev - 1));
      }
    } catch (error) {
      console.error('Error marking notification as read:', error);
      toast.error('Greška pri označavanju notifikacije');
    }
  };

  const handleMarkAllAsRead = async () => {
    if (!user) return;

    try {
      const { error } = await markAllNotificationsAsRead(user.id);
      if (error) {
        toast.error('Greška pri označavanju notifikacija');
      } else {
        setNotifications(prev => prev.map(n => ({ ...n, read: true })));
        setUnreadCount(0);
        toast.success('Sve notifikacije su označene kao pročitane');
      }
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      toast.error('Greška pri označavanju notifikacija');
    }
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'offer_received':
      case 'offer_accepted':
      case 'offer_rejected':
      case 'order_received':
        return <Inbox className="h-4 w-4" />;
      case 'campaign_application':
      case 'campaign_accepted':
      case 'campaign_rejected':
      case 'application_accepted_payment_pending':
        return <FileText className="h-4 w-4" />;
      case 'job_completion_submitted':
      case 'job_completion_approved':
      case 'job_completion_rejected':
        return <CheckCheck className="h-4 w-4" />;
      case 'message_received':
        return <MessageCircle className="h-4 w-4" />;
      case 'payment_received':
      case 'payment_required_offer_accepted':
      case 'payment_required_order_accepted':
      case 'payment_completed_work_ready':
        return <DollarSign className="h-4 w-4" />;
      default:
        return <Bell className="h-4 w-4" />;
    }
  };

  const getNotificationLink = (notification: Notification) => {
    switch (notification.type) {
      case 'offer_received':
      case 'offer_accepted':
      case 'offer_rejected':
      case 'payment_completed_work_ready':
        if (notification.data.offer_id || notification.data.project_id) {
          const id = notification.data.offer_id || notification.data.project_id;
          return `/dashboard/influencer/offers/${id}`;
        }
        return '/dashboard/influencer/offers';
      case 'order_received':
        if (notification.data.order_id) {
          return `/dashboard/influencer/offers/${notification.data.order_id}`;
        }
        return '/dashboard/influencer/offers';
      case 'payment_required_offer_accepted':
      case 'payment_required_order_accepted':
        if (notification.data.offer_id) {
          return `/dashboard/biznis/offers/${notification.data.offer_id}`;
        }
        if (notification.data.order_id) {
          return `/dashboard/biznis/offers/${notification.data.order_id}`;
        }
        return '/dashboard/biznis/offers';
      case 'job_completion_submitted':
        if (notification.data.direct_offer_id) {
          return `/dashboard/biznis/offers/${notification.data.direct_offer_id}`;
        }
        return '/dashboard/biznis/offers';
      case 'job_completion_approved':
      case 'job_completion_rejected':
        if (notification.data.job_completion_id) {
          return '/dashboard/job-completions';
        }
        return '/dashboard/influencer/offers';
      case 'campaign_application':
        return '/dashboard/campaigns';
      case 'campaign_accepted':
      case 'campaign_rejected':
      case 'application_accepted_payment_pending':
        if (notification.data.application_id) {
          return `/dashboard/influencer/applications/${notification.data.application_id}`;
        }
        return '/dashboard/influencer/campaigns';
      case 'message_received':
        return '/dashboard/messages';
      case 'payment_received':
        return '/dashboard/influencer/earnings';
      default:
        return '/dashboard';
    }
  };

  if (!user) return null;

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="relative p-2 rounded-xl hover:bg-gradient-to-r hover:from-accent/80 hover:to-accent/60 transition-all duration-300 group"
        >
          <Bell className="h-5 w-5 group-hover:scale-110 transition-transform duration-300" />
          {unreadCount > 0 && (
            <Badge className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs bg-gradient-to-r from-[#F35BF6] to-[#f04a13] text-white border-0 shadow-lg shadow-pink-500/25 animate-pulse">
              {unreadCount > 99 ? '99+' : unreadCount}
            </Badge>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        className="w-96 bg-card/95 backdrop-blur-md border border-purple-500/20 shadow-2xl shadow-purple-500/20 rounded-2xl overflow-hidden"
      >
        <DropdownMenuLabel className="relative flex items-center justify-between p-4 bg-gradient-to-br from-[#7F5BFE]/10 via-[#F35BF6]/5 to-[#f04a13]/10 border-b border-purple-500/10">
          {/* Decorative background pattern */}
          <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-pink-500/5 opacity-50"></div>
          <div className="absolute top-0 right-0 w-16 h-16 bg-gradient-to-br from-purple-400/10 to-transparent rounded-full blur-xl"></div>
          <div className="absolute bottom-0 left-0 w-12 h-12 bg-gradient-to-tr from-pink-400/10 to-transparent rounded-full blur-lg"></div>

          <div className="relative flex items-center space-x-2">
            <div className="flex items-center justify-center w-8 h-8 rounded-lg bg-gradient-to-br from-purple-500/20 to-pink-500/20">
              <Bell className="h-4 w-4 text-purple-600 dark:text-purple-400" />
            </div>
            <h3 className="font-bold text-foreground">Notifikacije</h3>
            {unreadCount > 0 && (
              <Badge className="bg-gradient-to-r from-[#F35BF6] to-[#f04a13] text-white border-0 text-xs">
                {unreadCount}
              </Badge>
            )}
          </div>
          {unreadCount > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleMarkAllAsRead}
              className="relative text-xs hover:bg-gradient-to-r hover:from-purple-500/10 hover:to-pink-500/10 transition-all duration-300 rounded-lg group"
            >
              <CheckCheck className="h-3 w-3 mr-1 group-hover:scale-110 transition-transform duration-300" />
              Označi sve
            </Button>
          )}
        </DropdownMenuLabel>
        <DropdownMenuSeparator />

        <ScrollArea className="h-96">
          {isLoading ? (
            <div className="flex items-center justify-center p-8">
              <div className="animate-spin rounded-full h-6 w-6 border-2 border-purple-500/20 border-t-purple-500"></div>
            </div>
          ) : notifications.length === 0 ? (
            <div className="flex flex-col items-center justify-center p-8 text-center">
              <div className="flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-br from-purple-500/10 to-pink-500/10 mb-4">
                <Bell className="h-8 w-8 text-purple-500/60" />
              </div>
              <p className="text-sm text-muted-foreground font-medium">
                Nema novih notifikacija
              </p>
              <p className="text-xs text-muted-foreground/70 mt-1">
                Ovdje će se pojaviti vaše notifikacije
              </p>
            </div>
          ) : (
            <div className="p-2 space-y-2">
              {notifications.map(notification => (
                <div
                  key={notification.id}
                  className={`relative rounded-xl border transition-all duration-300 hover:shadow-md group ${
                    notification.read
                      ? 'border-border/50 bg-card/50 hover:bg-gradient-to-r hover:from-accent/30 hover:to-accent/20'
                      : 'border-purple-500/30 bg-gradient-to-r from-purple-500/5 via-pink-500/3 to-orange-500/5 hover:from-purple-500/10 hover:via-pink-500/5 hover:to-orange-500/10 shadow-sm shadow-purple-500/10'
                  }`}
                >
                  {!notification.read && (
                    <div className="absolute top-2 left-2 w-2 h-2 bg-gradient-to-r from-[#F35BF6] to-[#f04a13] rounded-full animate-pulse"></div>
                  )}

                  <Link
                    href={getNotificationLink(notification)}
                    onClick={() => {
                      if (!notification.read) {
                        handleMarkAsRead(notification.id);
                      }
                      setIsOpen(false);
                    }}
                    className="block p-4"
                  >
                    <div className="flex items-start gap-3">
                      <div
                        className={`flex-shrink-0 mt-0.5 flex items-center justify-center w-8 h-8 rounded-lg transition-all duration-300 group-hover:scale-110 ${
                          notification.read
                            ? 'bg-muted/50'
                            : 'bg-gradient-to-br from-purple-500/20 to-pink-500/20'
                        }`}
                      >
                        <div
                          className={
                            notification.read
                              ? 'text-muted-foreground'
                              : 'text-purple-600 dark:text-purple-400'
                          }
                        >
                          {getNotificationIcon(notification.type)}
                        </div>
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                          <h4
                            className={`text-sm font-semibold transition-colors duration-300 ${
                              notification.read
                                ? 'text-muted-foreground group-hover:text-foreground'
                                : 'text-foreground'
                            }`}
                          >
                            {notification.title}
                          </h4>
                          {!notification.read && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={e => {
                                e.preventDefault();
                                e.stopPropagation();
                                handleMarkAsRead(notification.id);
                              }}
                              className="h-6 w-6 p-0 ml-2 hover:bg-gradient-to-r hover:from-purple-500/20 hover:to-pink-500/20 transition-all duration-300 rounded-lg group/btn"
                            >
                              <Check className="h-3 w-3 group-hover/btn:scale-110 transition-transform duration-300" />
                            </Button>
                          )}
                        </div>
                        <p
                          className={`text-xs mt-1 transition-colors duration-300 ${
                            notification.read
                              ? 'text-muted-foreground/80 group-hover:text-muted-foreground'
                              : 'text-muted-foreground'
                          }`}
                        >
                          {notification.message}
                        </p>
                        <p className="text-xs text-muted-foreground/60 mt-2 font-medium">
                          {formatDistanceToNow(
                            new Date(notification.created_at),
                            {
                              addSuffix: true,
                              locale: hr,
                            }
                          )}
                        </p>
                      </div>
                    </div>
                  </Link>
                </div>
              ))}
            </div>
          )}
        </ScrollArea>

        {notifications.length > 0 && (
          <>
            <DropdownMenuSeparator className="bg-gradient-to-r from-transparent via-purple-500/20 to-transparent" />
            <div className="p-3">
              <Button
                variant="ghost"
                size="sm"
                className="w-full hover:bg-gradient-to-r hover:from-purple-500/10 hover:via-pink-500/5 hover:to-orange-500/10 transition-all duration-300 rounded-xl border border-transparent hover:border-purple-500/20 group"
                asChild
              >
                <Link
                  href="/dashboard/notifications"
                  className="flex items-center justify-center space-x-2"
                >
                  <span className="font-medium group-hover:text-purple-700 dark:group-hover:text-purple-300 transition-colors duration-300">
                    Pogledaj sve notifikacije
                  </span>
                  <div className="w-1 h-1 bg-purple-500 rounded-full group-hover:scale-150 transition-transform duration-300"></div>
                </Link>
              </Button>
            </div>
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
