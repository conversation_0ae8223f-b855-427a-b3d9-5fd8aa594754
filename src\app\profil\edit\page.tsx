'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { getProfile } from '@/lib/profiles';
import { Loader2 } from 'lucide-react';

export default function EditProfilPage() {
  const router = useRouter();
  const { user } = useAuth();

  const {
    register,
    handleSubmit,
    formState: { errors },
    setError,
    setValue,
  } = useForm<EditInfluencerForm>({
    resolver: zodResolver(editInfluencerSchema),
  });

  useEffect(() => {
    if (!user) {
      router.push('/prijava');
      return;
    }

    // Redirect to new profile settings
    router.push('/dashboard/influencer/profile');
  }, [user, router]);

  const loadExistingData = async () => {
    try {
      setLoadingData(true);

      // Load influencer data
      const { data: influencerData, error: influencerError } =
        await getInfluencer(user!.id);
      if (influencerError || !influencerData) {
        router.push('/profil/kreiranje/influencer');
        return;
      }

      // Load categories
      const { data: categoriesData } = await getInfluencerCategories(user!.id);
      const categoryIds =
        categoriesData?.map((cat: any) => cat.category_id) || [];
      setSelectedCategories(categoryIds);

      // Load platforms
      const { data: platformsData } = await getInfluencerPlatforms(user!.id);
      const platforms =
        platformsData?.map((platform: any) => ({
          platform_id: platform.platform_id,
          handle: platform.handle || '',
          followers_count: platform.followers_count || 0,
          content_type_ids: [], // Will be filled from pricing data
        })) || [];

      // Load pricing
      const { data: pricingData } = await getInfluencerPricing(user!.id);
      const pricingEntries =
        pricingData?.map((price: any) => ({
          platform_id: price.platform_id,
          content_type_id: price.content_type_id,
          price: price.price,
          is_available: price.is_available,
        })) || [];

      // Fill content_type_ids from pricing data
      platforms.forEach((platform: any) => {
        platform.content_type_ids = pricingEntries
          .filter((price: any) => price.platform_id === platform.platform_id)
          .map((price: any) => price.content_type_id);
      });

      setSelectedPlatforms(platforms);
      setPricing(pricingEntries);

      // Set form values
      setValue('username', influencerData.profiles?.username || '');
      setValue('bio', influencerData.profiles?.bio || '');
      setValue('location', influencerData.profiles?.location || '');
      setValue('website', influencerData.profiles?.website_url || '');
      setValue('gender', influencerData.gender || undefined);
      setValue('age', influencerData.age || undefined);
    } catch (error) {
      console.error('Error loading data:', error);
      setError('root', { message: 'Greška pri učitavanju podataka' });
    } finally {
      setLoadingData(false);
    }
  };

  const onSubmit = async (data: EditInfluencerForm) => {
    if (!user) return;

    if (selectedCategories.length === 0) {
      setError('root', {
        message: 'Morate izabrati najmanje jednu kategoriju',
      });
      return;
    }

    if (selectedPlatforms.length === 0) {
      setError('root', { message: 'Morate izabrati najmanje jednu platformu' });
      return;
    }

    // Check if all platforms have at least one content type
    const platformsWithoutContent = selectedPlatforms.filter(
      p => p.content_type_ids.length === 0
    );
    if (platformsWithoutContent.length > 0) {
      setError('root', {
        message: 'Svaka platforma mora imati najmanje jedan content tip',
      });
      return;
    }

    setIsLoading(true);

    try {
      // Update basic profile
      const { error: profileError } = await updateProfile(user.id, {
        username: data.username,
        bio: data.bio || null,
        location: data.location || null,
        website_url: data.website || null,
      });

      if (profileError) {
        if (profileError.message.includes('username')) {
          setError('username', { message: 'Username je već zauzet' });
        } else {
          setError('root', { message: 'Greška pri ažuriranju profila' });
        }
        return;
      }

      // Update influencer specific data
      const { error: influencerError } = await updateInfluencer(user.id, {
        gender: data.gender || null,
        age: data.age || null,
      });

      if (influencerError) {
        setError('root', {
          message: 'Greška pri ažuriranju influencer profila',
        });
        return;
      }

      // Update categories
      const { error: categoriesError } = await updateInfluencerCategories(
        user.id,
        selectedCategories
      );
      if (categoriesError) {
        setError('root', { message: 'Greška pri ažuriranju kategorija' });
        return;
      }

      // Update platforms
      const { error: platformsError } = await updateInfluencerPlatforms(
        user.id,
        selectedPlatforms
      );
      if (platformsError) {
        setError('root', { message: 'Greška pri ažuriranju platformi' });
        return;
      }

      // Update pricing
      const { error: pricingError } = await updateInfluencerPricing(
        user.id,
        pricing
      );
      if (pricingError) {
        setError('root', { message: 'Greška pri ažuriranju cijena' });
        return;
      }

      // Redirect back to dashboard
      router.push('/dashboard/influencer');
    } catch (error) {
      setError('root', { message: 'Neočekivana greška. Pokušajte ponovo.' });
    } finally {
      setIsLoading(false);
    }
  };

  if (loadingData) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-muted-foreground">Učitavanje profila...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background flex flex-col">
      {/* Header */}
      <header className="border-b border-border">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <Link
            href="/dashboard/influencer"
            className="flex items-center space-x-2 text-muted-foreground hover:text-foreground"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Nazad na dashboard</span>
          </Link>
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
              <span className="text-primary-foreground font-bold text-lg">
                🔗
              </span>
            </div>
            <span className="text-xl font-bold text-foreground">Influexus</span>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1 px-4 py-8">
        <div className="container mx-auto max-w-2xl">
          <Card>
            <CardHeader className="text-center">
              <CardTitle className="text-2xl">Uredi profil</CardTitle>
              <CardDescription>
                Ažurirajte svoje informacije i postavke
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                {/* Categories */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Kategorije</h3>
                  <p className="text-sm text-muted-foreground">
                    Izaberite kategorije u kojima kreirate sadržaj (minimum 1,
                    maksimum 3)
                  </p>
                  <CategorySelector
                    selectedCategories={selectedCategories}
                    onCategoriesChange={setSelectedCategories}
                    maxCategories={3}
                    placeholder="Izaberite vaše kategorije..."
                  />
                </div>

                {/* Basic Info */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Osnovne informacije</h3>

                  <div className="space-y-2">
                    <Label htmlFor="username">Username *</Label>
                    <Input
                      id="username"
                      placeholder="marko_markovic"
                      {...register('username')}
                      className={errors.username ? 'border-red-500' : ''}
                    />
                    {errors.username && (
                      <p className="text-sm text-red-500">
                        {errors.username.message}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="bio">Bio</Label>
                    <Textarea
                      id="bio"
                      placeholder="Opišite sebe u nekoliko rečenica..."
                      rows={3}
                      {...register('bio')}
                      className={errors.bio ? 'border-red-500' : ''}
                    />
                    {errors.bio && (
                      <p className="text-sm text-red-500">
                        {errors.bio.message}
                      </p>
                    )}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="location">Lokacija</Label>
                      <Input
                        id="location"
                        placeholder="Sarajevo, BiH"
                        {...register('location')}
                        className={errors.location ? 'border-red-500' : ''}
                      />
                      {errors.location && (
                        <p className="text-sm text-red-500">
                          {errors.location.message}
                        </p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="website">Website</Label>
                      <Input
                        id="website"
                        placeholder="https://vaswebsite.com"
                        {...register('website')}
                        className={errors.website ? 'border-red-500' : ''}
                      />
                      {errors.website && (
                        <p className="text-sm text-red-500">
                          {errors.website.message}
                        </p>
                      )}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="gender">Pol</Label>
                      <select
                        id="gender"
                        {...register('gender')}
                        className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      >
                        <option value="">Izaberite...</option>
                        <option value="male">Muški</option>
                        <option value="female">Ženski</option>
                        <option value="other">Ostalo</option>
                        <option value="prefer_not_to_say">
                          Preferiram da ne kažem
                        </option>
                      </select>
                      {errors.gender && (
                        <p className="text-sm text-red-500">
                          {errors.gender.message}
                        </p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="age">Godine</Label>
                      <Input
                        id="age"
                        type="number"
                        placeholder="25"
                        min="13"
                        max="100"
                        {...register('age', { valueAsNumber: true })}
                        className={errors.age ? 'border-red-500' : ''}
                      />
                      {errors.age && (
                        <p className="text-sm text-red-500">
                          {errors.age.message}
                        </p>
                      )}
                    </div>
                  </div>
                </div>

                {/* Platforms */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Platforme i sadržaj</h3>
                  <PlatformSelector
                    selectedPlatforms={selectedPlatforms}
                    onPlatformsChange={setSelectedPlatforms}
                    disabled={isLoading}
                  />
                </div>

                {/* Pricing */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Cijene</h3>
                  <PricingMatrix
                    selectedPlatforms={selectedPlatforms}
                    pricing={pricing}
                    onPricingChange={setPricing}
                    disabled={isLoading}
                  />
                </div>

                {/* Submit Button */}
                <div className="flex gap-4">
                  <Button
                    type="button"
                    variant="outline"
                    className="flex-1"
                    onClick={() => router.push('/dashboard/influencer')}
                  >
                    Otkaži
                  </Button>
                  <Button type="submit" className="flex-1" disabled={isLoading}>
                    {isLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Čuvanje...
                      </>
                    ) : (
                      <>
                        <Save className="mr-2 h-4 w-4" />
                        Sačuvaj promjene
                      </>
                    )}
                  </Button>
                </div>

                {/* Root Error */}
                {errors.root && (
                  <p className="text-sm text-red-500 text-center">
                    {errors.root.message}
                  </p>
                )}
              </form>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
}
