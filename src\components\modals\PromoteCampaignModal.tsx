'use client';

import * as React from 'react';
import { useState } from 'react';
import { Crown, Clock, Euro, CheckCircle, Loader2 } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import {
  FEATURED_CAMPAIGN_PRICES,
  type FeaturedCampaignPrice,
  canPromoteCampaign,
  createFeaturedCampaignPaymentSession,
} from '@/lib/featured-campaigns';

interface PromoteCampaignModalProps {
  isOpen: boolean;
  onClose: () => void;
  campaign: {
    id: string;
    title: string;
    business_id: string;
  } | null;
  onSuccess?: () => void;
}

export function PromoteCampaignModal({
  isOpen,
  onClose,
  campaign,
  onSuccess,
}: PromoteCampaignModalProps) {
  const [selectedOption, setSelectedOption] = useState<7 | 14 | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const handlePromote = async () => {
    if (!campaign || !selectedOption) return;

    setIsLoading(true);
    try {
      // Check if campaign can be promoted
      const { canPromote, reason } = await canPromoteCampaign(
        campaign.id,
        campaign.business_id
      );

      if (!canPromote) {
        toast.error(reason || 'Kampanja ne može biti promovirana');
        setIsLoading(false);
        return;
      }

      // Create payment session using new Edge Function architecture
      console.log('Creating payment session for featured campaign:', {
        campaignId: campaign.id,
        businessId: campaign.business_id,
        durationDays: selectedOption,
      });

      const { url } = await createFeaturedCampaignPaymentSession(
        campaign.id,
        campaign.business_id,
        selectedOption
      );

      if (url) {
        // Redirect to Stripe Checkout
        window.location.href = url;
      } else {
        throw new Error('No checkout URL received');
      }
    } catch (error) {
      console.error('Greška pri promociji:', error);
      toast.error(
        error instanceof Error
          ? error.message
          : 'Neočekivana greška pri promociji kampanje'
      );
      setIsLoading(false);
    }
  };

  const selectedPrice = FEATURED_CAMPAIGN_PRICES.find(
    p => p.days === selectedOption
  );

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-lg max-h-[95vh] overflow-hidden flex flex-col">
        <DialogHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-yellow-100">
            <Crown className="h-6 w-6 text-yellow-600" />
          </div>
          <DialogTitle className="text-xl font-semibold">
            Promoviši kampanju
          </DialogTitle>
          <DialogDescription className="text-gray-600">
            {campaign && (
              <>
                Promoviši kampanju "
                <span className="font-medium">{campaign.title}</span>" da bude
                istaknuta na vrhu liste kampanja
              </>
            )}
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto px-1 pb-1">
          <div className="mt-6">
            {/* Pricing Options */}
            <div className="mb-6">
              <h4 className="mb-4 font-medium text-gray-900">
                Izaberi period promocije:
              </h4>
              <div className="space-y-3">
                {FEATURED_CAMPAIGN_PRICES.map(option => (
                  <div
                    key={option.days}
                    className={`
                    relative cursor-pointer rounded-lg border-2 p-4 transition-all duration-200
                    ${
                      selectedOption === option.days
                        ? 'border-yellow-400 bg-yellow-50 ring-2 ring-yellow-400 ring-opacity-20'
                        : 'border-gray-200 bg-white hover:border-gray-300 hover:bg-gray-50'
                    }
                  `}
                    onClick={() => setSelectedOption(option.days)}
                  >
                    <div className="flex items-center justify-between gap-3">
                      <div className="flex items-center space-x-3 flex-1 min-w-0">
                        <div
                          className={`
                        flex h-5 w-5 items-center justify-center rounded-full border-2 shrink-0
                        ${
                          selectedOption === option.days
                            ? 'border-yellow-400 bg-yellow-400'
                            : 'border-gray-300'
                        }
                      `}
                        >
                          {selectedOption === option.days && (
                            <CheckCircle className="h-3 w-3 text-white" />
                          )}
                        </div>
                        <div className="min-w-0 flex-1">
                          <div className="flex items-center space-x-2 flex-wrap">
                            <Clock className="h-4 w-4 text-gray-500 shrink-0" />
                            <span className="font-medium text-gray-900 text-sm sm:text-base">
                              {option.label}
                            </span>
                            {option.days === 14 && (
                              <Badge
                                variant="secondary"
                                className="bg-green-100 text-green-800 text-xs shrink-0"
                              >
                                Najbolja vrijednost
                              </Badge>
                            )}
                          </div>
                          <p className="text-xs sm:text-sm text-gray-600 mt-1 leading-tight">
                            {option.days === 7
                              ? 'Kratka promocija za brže rezultate'
                              : 'Dugotrajan boost sa boljom cijenom'}
                          </p>
                        </div>
                      </div>
                      <div className="text-right shrink-0">
                        <div className="flex items-center space-x-1">
                          <Euro className="h-4 w-4 text-gray-500" />
                          <span className="text-lg font-bold text-gray-900">
                            {option.price.toFixed(2)}
                          </span>
                        </div>
                        <p className="text-xs text-gray-500 whitespace-nowrap">
                          €{(option.price / option.days).toFixed(2)}/dan
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Benefits */}
            <div className="mb-6 rounded-lg bg-gray-50 p-4">
              <h4 className="mb-3 font-medium text-gray-900">
                Benefiti featured kampanje:
              </h4>
              <ul className="space-y-2">
                {[
                  'Kampanja se prikazuje na vrhu liste',
                  'Istaknuta sa zlatnim badge-om',
                  'Višestruko više pregleda i prijava',
                  'Prioritetno rangiranje u pretrazi',
                ].map((benefit, index) => (
                  <li
                    key={index}
                    className="flex items-center text-sm text-gray-600"
                  >
                    <CheckCircle className="mr-2 h-4 w-4 text-green-500" />
                    {benefit}
                  </li>
                ))}
              </ul>
            </div>

            {/* Action Buttons */}
            <div className="flex space-x-3">
              <Button
                variant="outline"
                className="flex-1"
                onClick={onClose}
                disabled={isLoading}
              >
                Otkaži
              </Button>
              <Button
                className="flex-1 bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-gray-900 font-semibold"
                onClick={handlePromote}
                disabled={!selectedOption || isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Procesiranje...
                  </>
                ) : selectedPrice ? (
                  <>
                    <Crown className="mr-2 h-4 w-4" />
                    Promoviši za €{selectedPrice.price.toFixed(2)}
                  </>
                ) : (
                  'Izaberi opciju'
                )}
              </Button>
            </div>

            {/* Payment Note */}
            <div className="mt-4 rounded-lg bg-blue-50 border border-blue-200 p-3">
              <p className="text-xs text-blue-700">
                💳 <strong>Secure payment:</strong> Bićete preusmjereni na
                sigurnu Stripe stranicu za plaćanje. Kampanja će biti
                promovirana odmah nakon uspješnog plaćanja.
              </p>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
