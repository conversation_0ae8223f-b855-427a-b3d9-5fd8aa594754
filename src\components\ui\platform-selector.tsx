'use client';

import { useState, useEffect } from 'react';
import { Check, Plus, X, ChevronDown } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import { Badge } from '@/components/ui/badge';
import { supabase } from '@/lib/supabase';
import PlatformIconSimple from '@/components/ui/platform-icon-simple';

interface Platform {
  id: number;
  name: string;
  slug: string;
  icon: string;
}

interface ContentType {
  id: number;
  name: string;
  slug: string;
  description: string;
}

interface PlatformWithContentTypes {
  platform_id: number;
  platform_name: string;
  platform_slug: string;
  platform_icon: string;
  content_types: ContentType[];
}

interface SelectedPlatform {
  platform_id: number;
  handle: string;
  followers_count: number;
  content_type_ids: number[];
}

interface PlatformSelectorProps {
  selectedPlatforms: SelectedPlatform[];
  onPlatformsChange: (platforms: SelectedPlatform[]) => void;
  className?: string;
  disabled?: boolean;
}

export function PlatformSelector({
  selectedPlatforms,
  onPlatformsChange,
  className,
  disabled = false,
}: PlatformSelectorProps) {
  const [platforms, setPlatforms] = useState<PlatformWithContentTypes[]>([]);
  const [loading, setLoading] = useState(true);
  const [openPlatforms, setOpenPlatforms] = useState<Set<number>>(new Set());

  useEffect(() => {
    loadPlatforms();
  }, []);

  const loadPlatforms = async () => {
    try {
      setLoading(true);

      // Get platforms with their content types using direct query
      const { data: platformsData, error: platformsError } = await supabase
        .from('platforms')
        .select(
          `
          id,
          name,
          slug,
          icon,
          content_types (
            id,
            name,
            slug,
            description
          )
        `
        )
        .eq('is_active', true)
        .eq('content_types.is_active', true)
        .order('name');

      if (platformsError) {
        console.error('Error loading platforms:', platformsError);
        return;
      }

      // Transform data to match expected format
      const data =
        platformsData?.map(platform => ({
          platform_id: platform.id,
          platform_name: platform.name,
          platform_slug: platform.slug,
          platform_icon: platform.icon,
          content_types: platform.content_types || [],
        })) || [];

      setPlatforms(data || []);
    } catch (error) {
      console.error('Error loading platforms:', error);
    } finally {
      setLoading(false);
    }
  };

  const isPlatformSelected = (platformId: number) => {
    return selectedPlatforms.some(p => p.platform_id === platformId);
  };

  const getSelectedPlatform = (platformId: number) => {
    return selectedPlatforms.find(p => p.platform_id === platformId);
  };

  const getPlatformById = (platformId: number) => {
    return platforms.find(p => p.platform_id === platformId);
  };

  const addPlatform = (platformId: number) => {
    if (!isPlatformSelected(platformId)) {
      const newPlatform: SelectedPlatform = {
        platform_id: platformId,
        handle: '',
        followers_count: 0,
        content_type_ids: [],
      };
      onPlatformsChange([...selectedPlatforms, newPlatform]);
      setOpenPlatforms(prev => new Set([...prev, platformId]));
    }
  };

  const removePlatform = (platformId: number) => {
    onPlatformsChange(
      selectedPlatforms.filter(p => p.platform_id !== platformId)
    );
    setOpenPlatforms(prev => {
      const newSet = new Set(prev);
      newSet.delete(platformId);
      return newSet;
    });
  };

  const updatePlatform = (
    platformId: number,
    updates: Partial<SelectedPlatform>
  ) => {
    onPlatformsChange(
      selectedPlatforms.map(p =>
        p.platform_id === platformId ? { ...p, ...updates } : p
      )
    );
  };

  const toggleContentType = (platformId: number, contentTypeId: number) => {
    const platform = getSelectedPlatform(platformId);
    if (!platform) return;

    const contentTypeIds = platform.content_type_ids.includes(contentTypeId)
      ? platform.content_type_ids.filter(id => id !== contentTypeId)
      : [...platform.content_type_ids, contentTypeId];

    updatePlatform(platformId, { content_type_ids: contentTypeIds });
  };

  const togglePlatformOpen = (platformId: number) => {
    setOpenPlatforms(prev => {
      const newSet = new Set(prev);
      if (newSet.has(platformId)) {
        newSet.delete(platformId);
      } else {
        newSet.add(platformId);
      }
      return newSet;
    });
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="h-10 w-full bg-muted animate-pulse rounded-md"></div>
        <div className="h-32 w-full bg-muted animate-pulse rounded-md"></div>
      </div>
    );
  }

  return (
    <div className={cn('space-y-4', className)}>
      {/* Available Platforms */}
      <div>
        <Label className="text-base font-medium">Dostupne platforme</Label>
        <p className="text-sm text-muted-foreground mb-3">
          Izaberite platforme na kojima ste aktivni
        </p>
        <div className="flex flex-wrap gap-2">
          {platforms.map(platform => (
            <Button
              key={platform.platform_id}
              variant={
                isPlatformSelected(platform.platform_id) ? 'default' : 'outline'
              }
              size="sm"
              onClick={() => addPlatform(platform.platform_id)}
              disabled={disabled || isPlatformSelected(platform.platform_id)}
              className="flex items-center gap-2"
            >
              <PlatformIconSimple platform={platform.platform_name} size="md" />
              <span>{platform.platform_name}</span>
              {isPlatformSelected(platform.platform_id) && (
                <Check className="h-4 w-4" />
              )}
            </Button>
          ))}
        </div>
      </div>

      {/* Selected Platforms */}
      {selectedPlatforms.length > 0 && (
        <div className="space-y-3">
          <Label className="text-base font-medium">Vaše platforme</Label>
          {selectedPlatforms.map(selectedPlatform => {
            const platform = getPlatformById(selectedPlatform.platform_id);
            if (!platform) return null;

            const isOpen = openPlatforms.has(platform.platform_id);

            return (
              <Card key={platform.platform_id}>
                <Collapsible
                  open={isOpen}
                  onOpenChange={() => togglePlatformOpen(platform.platform_id)}
                >
                  <CollapsibleTrigger asChild>
                    <CardHeader className="cursor-pointer hover:bg-muted/50 transition-colors">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <PlatformIconSimple
                            platform={platform.platform_name}
                            size="xl"
                          />
                          <div>
                            <CardTitle className="text-lg">
                              {platform.platform_name}
                            </CardTitle>
                            <CardDescription>
                              {selectedPlatform.handle &&
                                `@${selectedPlatform.handle}`}
                              {selectedPlatform.followers_count > 0 &&
                                ` • ${selectedPlatform.followers_count.toLocaleString()} pratilaca`}
                              {selectedPlatform.content_type_ids.length > 0 &&
                                ` • ${selectedPlatform.content_type_ids.length} content tipova`}
                            </CardDescription>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={e => {
                              e.stopPropagation();
                              removePlatform(platform.platform_id);
                            }}
                            disabled={disabled}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                          <ChevronDown
                            className={cn(
                              'h-4 w-4 transition-transform',
                              isOpen && 'transform rotate-180'
                            )}
                          />
                        </div>
                      </div>
                    </CardHeader>
                  </CollapsibleTrigger>

                  <CollapsibleContent>
                    <CardContent className="space-y-4">
                      {/* Platform Details */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor={`handle-${platform.platform_id}`}>
                            Handle/Username
                          </Label>
                          <Input
                            id={`handle-${platform.platform_id}`}
                            placeholder={`Vaš ${platform.platform_name} username`}
                            value={selectedPlatform.handle}
                            onChange={e =>
                              updatePlatform(platform.platform_id, {
                                handle: e.target.value,
                              })
                            }
                            disabled={disabled}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor={`followers-${platform.platform_id}`}>
                            Broj pratilaca
                          </Label>
                          <Input
                            id={`followers-${platform.platform_id}`}
                            type="number"
                            placeholder="0"
                            value={selectedPlatform.followers_count || ''}
                            onChange={e =>
                              updatePlatform(platform.platform_id, {
                                followers_count: parseInt(e.target.value) || 0,
                              })
                            }
                            disabled={disabled}
                          />
                        </div>
                      </div>

                      {/* Content Types */}
                      <div className="space-y-3">
                        <Label>Content tipovi koje nudite</Label>
                        <p className="text-sm text-muted-foreground">
                          Izaberite tipove sadržaja koje kreirate na{' '}
                          {platform.platform_name}
                        </p>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                          {platform.content_types.map(contentType => (
                            <div
                              key={`${platform.id}-${contentType.id}`}
                              className={cn(
                                'p-3 border rounded-lg cursor-pointer transition-colors',
                                selectedPlatform.content_type_ids.includes(
                                  contentType.id
                                )
                                  ? 'border-primary bg-primary/5'
                                  : 'border-border hover:border-primary/50'
                              )}
                              onClick={() =>
                                !disabled &&
                                toggleContentType(
                                  platform.platform_id,
                                  contentType.id
                                )
                              }
                            >
                              <div className="flex items-center justify-between">
                                <div>
                                  <div className="font-medium text-sm">
                                    {contentType.name}
                                  </div>
                                  <div className="text-xs text-muted-foreground">
                                    {contentType.description}
                                  </div>
                                </div>
                                {selectedPlatform.content_type_ids.includes(
                                  contentType.id
                                ) && <Check className="h-4 w-4 text-primary" />}
                              </div>
                            </div>
                          ))}
                        </div>

                        {/* Selected Content Types Summary */}
                        {selectedPlatform.content_type_ids.length > 0 && (
                          <div className="flex flex-wrap gap-1">
                            {selectedPlatform.content_type_ids.map(
                              contentTypeId => {
                                const contentType = platform.content_types.find(
                                  ct => ct.id === contentTypeId
                                );
                                return contentType ? (
                                  <Badge
                                    key={`${platform.platform_id}-badge-${contentTypeId}`}
                                    variant="secondary"
                                    className="text-xs"
                                  >
                                    {contentType.name}
                                  </Badge>
                                ) : null;
                              }
                            )}
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </CollapsibleContent>
                </Collapsible>
              </Card>
            );
          })}
        </div>
      )}

      {/* Helper Text */}
      <div className="text-xs text-muted-foreground">
        {selectedPlatforms.length === 0 && (
          <span>Izaberite najmanje jednu platformu na kojoj ste aktivni</span>
        )}
        {selectedPlatforms.length > 0 && (
          <span>
            Izabrali ste {selectedPlatforms.length} platformu(e). Za svaku
            platformu izaberite content tipove koje nudite.
          </span>
        )}
      </div>
    </div>
  );
}
