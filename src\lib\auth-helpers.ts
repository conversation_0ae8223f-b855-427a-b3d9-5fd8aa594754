import { supabase } from './supabase';

export interface EmailExistsResponse {
  exists: boolean;
  error?: string;
}

/**
 * Provjerava da li korisnik sa datom email adresom već postoji
 */
export const checkEmailExists = async (
  email: string
): Promise<EmailExistsResponse> => {
  try {
    // Koristi SQL query da provjeri auth.users tabelu
    const { data, error } = await supabase.rpc('check_user_exists', {
      user_email: email,
    });

    if (error) {
      return { exists: false, error: error.message };
    }

    return { exists: !!data };
  } catch (error) {
    return {
      exists: false,
      error: error instanceof Error ? error.message : 'Neočekivana greška',
    };
  }
};

/**
 * Pravi "safe" registraciju koja prvo provjerava da li email postoji
 */
export const safeSignUp = async (
  email: string,
  password: string,
  userData: {
    full_name?: string;
    company_name?: string;
    user_type: 'influencer' | 'business';
  }
) => {
  try {
    // Prvo provjeri da li korisnik već postoji
    const emailCheck = await checkEmailExists(email);

    if (emailCheck.exists) {
      return {
        data: null,
        error: {
          message: 'User already registered',
          status: 422,
        },
      };
    }

    if (emailCheck.error) {
      return {
        data: null,
        error: { message: emailCheck.error },
      };
    }

    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          full_name: userData.full_name,
          user_type: userData.user_type,
        },
      },
    });

    // Ako je registracija uspješna i user postoji, kreiraj profile i business record odmah
    if (data.user && !error) {
      try {
        // Kreiraj profile record
        await supabase.from('profiles').insert({
          id: data.user.id,
          full_name: userData.full_name,
          user_type: userData.user_type,
          onboarding_step: 1,
        });

        // Ako je business user, kreiraj i business record sa official_company_name
        if (userData.user_type === 'business' && userData.company_name) {
          await supabase.from('businesses').insert({
            id: data.user.id,
            official_company_name: userData.company_name,
          });
        }
      } catch (dbError) {
        console.error('Error creating profile/business records:', dbError);
        // Ne vraćamo error jer je auth uspješan, samo logujemo
      }
    }

    return { data, error };
  } catch (error) {
    return {
      data: null,
      error: error instanceof Error ? error : new Error('Neočekivana greška'),
    };
  }
};
